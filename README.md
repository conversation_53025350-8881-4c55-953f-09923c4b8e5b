# 胡小群数学多媒体学习平台

## 项目简介

基于胡小群老师小学1-2年级数学复习文档，构建的系统化、多媒体化、高效有趣的数学学习平台。

## 核心理念

- **数学思想优于数学知识**：重视"为什么"胜过"怎么做"
- **对应思想贯穿始终**：从具体操作到抽象概念的思维桥梁
- **深度理解与趣味结合**：在游戏化中培养数学思维

## 教学内容体系

### 第一阶段：基础概念建立
1. **从对应到计数** - 对应思想的建立与计数概念
2. **对应与十进制** - 位值概念和进位规律
3. **数的大小和比较** - 比较方法和大小关系

### 第二阶段：运算意义理解  
4. **加减法的奥秘** - 运算本质和思维方式
5. **凑十、平十和破十** - 计算策略和思维技巧

### 第三阶段：计算技能与应用
6. **加减竖式运算** - 算法理解和操作规范
7. **数字谜基础** - 逻辑推理和问题解决
8. **加减巧算** - 计算优化和思维灵活性
9. **数字谜进阶** - 综合应用和创新思维

## 技术架构

- **前端框架**：React + TypeScript
- **3D动画**：Three.js
- **2D互动**：Canvas API
- **状态管理**：Redux Toolkit
- **样式方案**：Tailwind CSS
- **构建工具**：Vite

## 功能特色

- 🎯 **个性化学习路径**：根据学生基础自适应调整
- 🎮 **游戏化学习**：寓教于乐的互动体验
- 📊 **进度追踪**：详细的学习数据分析
- 👨‍👩‍👧‍👦 **家长指导**：科学的辅导建议和方法

## 开发计划

查看详细的开发进度和任务分配。

## 贡献指南

欢迎参与项目开发和改进建议。

## 许可证

MIT License