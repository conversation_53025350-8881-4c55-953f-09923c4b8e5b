import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Student } from '@/types';

// 状态接口
interface AuthState {
  isAuthenticated: boolean;
  currentUser: Student | null;
  loading: boolean;
  error: string | null;
  sessionExpiry: number | null;
}

// 初始状态
const initialState: AuthState = {
  isAuthenticated: false,
  currentUser: null,
  loading: false,
  error: null,
  sessionExpiry: null,
};

// 异步actions
export const loginStudent = createAsyncThunk(
  'auth/loginStudent',
  async (credentials: { username: string; password?: string }, { rejectWithValue }) => {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 创建模拟学生数据
      const student: Student = {
        id: `student_${Date.now()}`,
        name: credentials.username,
        age: 7,
        grade: 1,
        avatar: '/avatars/default.png',
        createdAt: new Date(),
        lastActiveAt: new Date(),
        preferences: {
          theme: 'colorful',
          soundEnabled: true,
          animationSpeed: 'normal',
          difficulty: 'normal',
          language: 'zh-CN',
        },
        progress: {
          completedLessons: [],
          currentLesson: 'lesson-01',
          totalScore: 0,
          studyTime: 0,
          streakDays: 0,
          lastStudyDate: new Date(),
          weakAreas: [],
          strongAreas: [],
          learningPath: ['lesson-01'],
        },
        achievements: [],
      };
      
      return student;
    } catch (error) {
      return rejectWithValue('登录失败，请重试');
    }
  }
);

export const logoutStudent = createAsyncThunk(
  'auth/logoutStudent',
  async (_, { getState }) => {
    // 保存学习进度
    const state = getState() as any;
    const student = state.auth.currentUser;
    
    if (student) {
      // 这里可以添加保存数据到服务器的逻辑
      console.log('保存学生数据:', student);
    }
    
    return null;
  }
);

export const refreshSession = createAsyncThunk(
  'auth/refreshSession',
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as any;
      const currentUser = state.auth.currentUser;
      
      if (!currentUser) {
        throw new Error('用户未登录');
      }
      
      // 更新最后活跃时间
      const updatedUser = {
        ...currentUser,
        lastActiveAt: new Date(),
      };
      
      return updatedUser;
    } catch (error) {
      return rejectWithValue('会话刷新失败');
    }
  }
);

// 创建slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateUserPreferences: (state, action: PayloadAction<Partial<Student['preferences']>>) => {
      if (state.currentUser) {
        state.currentUser.preferences = {
          ...state.currentUser.preferences,
          ...action.payload,
        };
      }
    },
    setSessionExpiry: (state, action: PayloadAction<number>) => {
      state.sessionExpiry = action.payload;
    },
    extendSession: (state) => {
      if (state.sessionExpiry) {
        state.sessionExpiry = Date.now() + 60 * 60 * 1000; // 延长1小时
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // 登录
      .addCase(loginStudent.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loginStudent.fulfilled, (state, action) => {
        state.loading = false;
        state.isAuthenticated = true;
        state.currentUser = action.payload;
        state.sessionExpiry = Date.now() + 60 * 60 * 1000; // 1小时后过期
        state.error = null;
      })
      .addCase(loginStudent.rejected, (state, action) => {
        state.loading = false;
        state.isAuthenticated = false;
        state.currentUser = null;
        state.error = action.payload as string;
      })
      // 登出
      .addCase(logoutStudent.fulfilled, (state) => {
        state.isAuthenticated = false;
        state.currentUser = null;
        state.sessionExpiry = null;
        state.error = null;
      })
      // 刷新会话
      .addCase(refreshSession.fulfilled, (state, action) => {
        state.currentUser = action.payload;
        state.sessionExpiry = Date.now() + 60 * 60 * 1000;
      })
      .addCase(refreshSession.rejected, (state) => {
        state.isAuthenticated = false;
        state.currentUser = null;
        state.sessionExpiry = null;
      });
  },
});

// 导出actions
export const { 
  clearError, 
  updateUserPreferences, 
  setSessionExpiry, 
  extendSession 
} = authSlice.actions;

// 选择器
export const selectAuth = (state: { auth: AuthState }) => state.auth;
export const selectCurrentUser = (state: { auth: AuthState }) => state.auth.currentUser;
export const selectIsAuthenticated = (state: { auth: AuthState }) => state.auth.isAuthenticated;
export const selectAuthLoading = (state: { auth: AuthState }) => state.auth.loading;
export const selectAuthError = (state: { auth: AuthState }) => state.auth.error;

export default authSlice.reducer;