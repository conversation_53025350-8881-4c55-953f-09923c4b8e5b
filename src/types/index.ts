// 课程相关类型
export interface Lesson {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  duration: number; // 预计学习时间（分钟）
  difficulty: 1 | 2 | 3 | 4 | 5; // 难度等级
  prerequisites: string[]; // 前置课程ID
  concepts: string[]; // 涉及的数学概念
  activities: Activity[];
  status: 'locked' | 'available' | 'in_progress' | 'completed';
}

// 学习活动类型
export interface Activity {
  id: string;
  type: 'animation' | 'interactive' | 'game' | 'exercise' | 'quiz';
  title: string;
  description: string;
  config: ActivityConfig;
  completed: boolean;
  score?: number;
  attempts: number;
}

// 活动配置（联合类型）
export type ActivityConfig = 
  | AnimationConfig 
  | InteractiveConfig 
  | GameConfig 
  | ExerciseConfig 
  | QuizConfig;

// 动画配置
export interface AnimationConfig {
  type: 'animation';
  duration: number;
  autoPlay: boolean;
  controls: boolean;
  scenes: AnimationScene[];
}

export interface AnimationScene {
  id: string;
  duration: number;
  narration?: string;
  interactions?: Interaction[];
}

// 交互配置
export interface InteractiveConfig {
  type: 'interactive';
  tool: 'counting' | 'comparison' | 'calculation' | 'visualization';
  instructions: string;
  hints: string[];
  validation: ValidationRule[];
}

// 游戏配置
export interface GameConfig {
  type: 'game';
  gameType: 'matching' | 'sorting' | 'puzzle' | 'challenge';
  rules: string;
  levels: GameLevel[];
  timeLimit?: number;
  maxAttempts?: number;
}

export interface GameLevel {
  id: string;
  difficulty: number;
  objectives: string[];
  successCriteria: SuccessCriteria;
}

// 练习配置
export interface ExerciseConfig {
  type: 'exercise';
  problems: Problem[];
  randomOrder: boolean;
  showHints: boolean;
  allowRetry: boolean;
}

// 测验配置
export interface QuizConfig {
  type: 'quiz';
  questions: Question[];
  timeLimit?: number;
  passingScore: number;
  showResults: boolean;
}

// 数学问题
export interface Problem {
  id: string;
  type: 'calculation' | 'word_problem' | 'number_puzzle' | 'comparison';
  question: string;
  answer: string | number;
  explanation: string;
  hints: string[];
  difficulty: number;
  concepts: string[];
}

// 测验问题
export interface Question {
  id: string;
  type: 'multiple_choice' | 'true_false' | 'fill_blank' | 'drag_drop';
  question: string;
  options?: string[];
  correctAnswer: string | number | boolean;
  explanation: string;
  points: number;
}

// 交互元素
export interface Interaction {
  type: 'click' | 'drag' | 'input' | 'select';
  target: string;
  action: string;
  feedback: string;
}

// 验证规则
export interface ValidationRule {
  field: string;
  rule: 'required' | 'numeric' | 'range' | 'exact';
  value?: any;
  message: string;
}

// 成功标准
export interface SuccessCriteria {
  score?: number;
  accuracy?: number;
  time?: number;
  attempts?: number;
}

// 学生信息
export interface Student {
  id: string;
  name: string;
  age: number;
  grade: 1 | 2;
  avatar: string;
  createdAt: Date;
  lastActiveAt: Date;
  preferences: StudentPreferences;
  progress: StudentProgress;
  achievements: Achievement[];
}

// 学生偏好设置
export interface StudentPreferences {
  theme: 'light' | 'dark' | 'colorful';
  soundEnabled: boolean;
  animationSpeed: 'slow' | 'normal' | 'fast';
  difficulty: 'easy' | 'normal' | 'hard';
  language: 'zh-CN' | 'en-US';
}

// 学生学习进度
export interface StudentProgress {
  completedLessons: string[];
  currentLesson: string;
  totalScore: number;
  studyTime: number; // 总学习时间（分钟）
  streakDays: number; // 连续学习天数
  lastStudyDate: Date;
  weakAreas: string[]; // 薄弱环节
  strongAreas: string[]; // 强项
  learningPath: string[]; // 个性化学习路径
}

// 成就系统
export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  type: 'progress' | 'skill' | 'streak' | 'special';
  condition: AchievementCondition;
  unlockedAt?: Date;
  progress: number; // 0-100
}

export interface AchievementCondition {
  type: 'lessons_completed' | 'score_achieved' | 'streak_maintained' | 'time_spent';
  target: number;
  context?: string;
}

// 学习会话
export interface StudySession {
  id: string;
  studentId: string;
  lessonId: string;
  startTime: Date;
  endTime?: Date;
  activities: ActivityResult[];
  totalScore: number;
  completed: boolean;
}

// 活动结果
export interface ActivityResult {
  activityId: string;
  startTime: Date;
  endTime: Date;
  score: number;
  attempts: number;
  hintsUsed: number;
  completed: boolean;
  timeSpent: number;
  errors: ActivityError[];
}

export interface ActivityError {
  timestamp: Date;
  errorType: string;
  context: string;
  userInput: any;
  correctAnswer: any;
}

// 家长相关
export interface Parent {
  id: string;
  name: string;
  email: string;
  children: string[]; // 学生ID数组
  notificationSettings: NotificationSettings;
}

export interface NotificationSettings {
  dailyProgress: boolean;
  achievements: boolean;
  difficulties: boolean;
  reminders: boolean;
  frequency: 'immediate' | 'daily' | 'weekly';
}

// 学习报告
export interface LearningReport {
  id: string;
  studentId: string;
  period: 'daily' | 'weekly' | 'monthly';
  startDate: Date;
  endDate: Date;
  summary: ReportSummary;
  details: ReportDetails;
  recommendations: string[];
  generatedAt: Date;
}

export interface ReportSummary {
  totalStudyTime: number;
  lessonsCompleted: number;
  averageScore: number;
  improvement: number; // 相比上期的提升百分比
  consistency: number; // 学习一致性评分
}

export interface ReportDetails {
  conceptMastery: ConceptMastery[];
  activityPerformance: ActivityPerformance[];
  learningPattern: LearningPattern;
  challengeAreas: string[];
  strengths: string[];
}

export interface ConceptMastery {
  concept: string;
  level: 'not_started' | 'learning' | 'practicing' | 'mastered';
  confidence: number; // 0-100
  lastPracticed: Date;
}

export interface ActivityPerformance {
  activityType: string;
  averageScore: number;
  averageTime: number;
  completionRate: number;
  improvementTrend: 'improving' | 'stable' | 'declining';
}

export interface LearningPattern {
  preferredTime: 'morning' | 'afternoon' | 'evening';
  sessionLength: number; // 平均学习时长
  breakFrequency: number; // 休息频率
  difficultyPreference: 'gradual' | 'challenge' | 'mixed';
}

// 应用状态
export interface AppState {
  auth: AuthState;
  student: StudentState;
  lesson: LessonState;
  ui: UIState;
}

export interface AuthState {
  isAuthenticated: boolean;
  currentUser: Student | null;
  loading: boolean;
  error: string | null;
}

export interface StudentState {
  current: Student | null;
  progress: StudentProgress | null;
  achievements: Achievement[];
  loading: boolean;
  error: string | null;
}

export interface LessonState {
  lessons: Lesson[];
  currentLesson: Lesson | null;
  currentActivity: Activity | null;
  loading: boolean;
  error: string | null;
}

export interface UIState {
  theme: 'light' | 'dark' | 'colorful';
  sidebar: boolean;
  modal: ModalState | null;
  notifications: Notification[];
  loading: boolean;
}

export interface ModalState {
  type: 'achievement' | 'help' | 'settings' | 'report';
  data?: any;
  visible: boolean;
}

export interface Notification {
  id: string;
  type: 'success' | 'warning' | 'error' | 'info';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  autoClose?: number; // 自动关闭时间（毫秒）
}