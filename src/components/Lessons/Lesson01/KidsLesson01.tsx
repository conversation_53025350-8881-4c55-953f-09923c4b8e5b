import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import KidsShepherdAnimation from './KidsShepherdAnimation';
import KidsCorrespondenceGame from './KidsCorrespondenceGame';

interface KidsLesson01Props {
  onComplete?: () => void;
}

type ActivityType = 'intro' | 'story' | 'game' | 'summary';

const KidsLesson01: React.FC<KidsLesson01Props> = ({ onComplete }) => {
  const [currentActivity, setCurrentActivity] = useState<ActivityType>('intro');
  const [completedActivities, setCompletedActivities] = useState<Set<ActivityType>>(new Set());
  const [totalScore, setTotalScore] = useState(0);

  const activities = [
    { id: 'intro', name: '开始学习', icon: '🌟', color: 'from-purple-400 to-purple-600' },
    { id: 'story', name: '听故事', icon: '📚', color: 'from-blue-400 to-blue-600' },
    { id: 'game', name: '玩游戏', icon: '🎮', color: 'from-green-400 to-green-600' },
    { id: 'summary', name: '总结', icon: '🏆', color: 'from-yellow-400 to-yellow-600' }
  ];

  const handleActivityComplete = (activity: ActivityType) => {
    setCompletedActivities(prev => new Set([...prev, activity]));
    
    // 自动进入下一个活动
    if (activity === 'intro') {
      setCurrentActivity('story');
    } else if (activity === 'story') {
      setCurrentActivity('game');
    } else if (activity === 'game') {
      setCurrentActivity('summary');
    }
  };

  const handleActivityClick = (activityId: ActivityType) => {
    setCurrentActivity(activityId);
  };

  const playSound = (type: 'click' | 'complete' | 'celebration') => {
    console.log(`🔊 Playing ${type} sound!`);
  };

  const renderIntro = () => (
    <motion.div
      className="w-full h-full flex flex-col items-center justify-center text-center p-8"
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
    >
      <motion.div
        className="text-9xl mb-8"
        animate={{ 
          rotate: [0, 10, -10, 0],
          scale: [1, 1.1, 1]
        }}
        transition={{ 
          duration: 2,
          repeat: Infinity
        }}
      >
        🌟
      </motion.div>
      
      <h1 className="text-5xl font-bold text-purple-800 mb-6">
        欢迎来到数学魔法世界！
      </h1>
      
      <p className="text-2xl text-purple-600 mb-8 max-w-2xl leading-relaxed">
        嗨！小朋友们！今天我们要学习一个超级有趣的数学魔法——
        <span className="text-pink-600 font-bold">对应与计数</span>！
      </p>
      
      <div className="bg-gradient-to-r from-pink-300 to-purple-300 rounded-3xl p-8 mb-8 max-w-3xl">
        <h2 className="text-3xl font-bold text-purple-800 mb-4">🎯 今天我们要学什么？</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-lg">
          <div className="bg-white rounded-2xl p-4 shadow-lg">
            <div className="text-4xl mb-2">🐑</div>
            <div className="font-bold text-purple-700">听牧羊人的故事</div>
            <div className="text-purple-600">学习古人是怎么数数的</div>
          </div>
          <div className="bg-white rounded-2xl p-4 shadow-lg">
            <div className="text-4xl mb-2">🏠</div>
            <div className="font-bold text-purple-700">帮小动物找家</div>
            <div className="text-purple-600">体验一一对应的魔法</div>
          </div>
        </div>
      </div>
      
      <motion.button
        onClick={() => handleActivityComplete('intro')}
        className="bg-gradient-to-r from-green-400 to-blue-400 text-white text-3xl font-bold px-12 py-6 rounded-full shadow-2xl border-4 border-white"
        whileHover={{ 
          scale: 1.1,
          boxShadow: "0 20px 40px rgba(0,0,0,0.2)"
        }}
        whileTap={{ scale: 0.95 }}
        animate={{ 
          y: [0, -10, 0],
        }}
        transition={{ 
          y: { duration: 2, repeat: Infinity }
        }}
      >
        🚀 开始魔法之旅！
      </motion.button>
    </motion.div>
  );

  const renderSummary = () => (
    <motion.div
      className="w-full h-full flex flex-col items-center justify-center text-center p-8"
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 50 }}
    >
      <motion.div
        className="text-9xl mb-8"
        animate={{ 
          rotate: 360,
          scale: [1, 1.2, 1]
        }}
        transition={{ 
          rotate: { duration: 3, repeat: Infinity },
          scale: { duration: 2, repeat: Infinity }
        }}
      >
        🏆
      </motion.div>
      
      <h1 className="text-5xl font-bold text-yellow-600 mb-6">
        🎉 恭喜你完成了魔法学习！
      </h1>
      
      <div className="bg-gradient-to-r from-yellow-300 to-orange-300 rounded-3xl p-8 mb-8 max-w-4xl">
        <h2 className="text-3xl font-bold text-orange-800 mb-6">✨ 你学会了什么魔法？</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <motion.div 
            className="bg-white rounded-2xl p-6 shadow-lg"
            whileHover={{ scale: 1.05, rotate: 2 }}
          >
            <div className="text-5xl mb-3">🧙‍♂️</div>
            <div className="font-bold text-purple-700 text-xl mb-2">对应魔法</div>
            <div className="text-purple-600">学会了一一对应的神奇方法</div>
          </motion.div>
          
          <motion.div 
            className="bg-white rounded-2xl p-6 shadow-lg"
            whileHover={{ scale: 1.05, rotate: -2 }}
          >
            <div className="text-5xl mb-3">🔢</div>
            <div className="font-bold text-blue-700 text-xl mb-2">计数魔法</div>
            <div className="text-blue-600">不用数字也能知道数量</div>
          </motion.div>
          
          <motion.div 
            className="bg-white rounded-2xl p-6 shadow-lg"
            whileHover={{ scale: 1.05, rotate: 2 }}
          >
            <div className="text-5xl mb-3">🏠</div>
            <div className="font-bold text-green-700 text-xl mb-2">配对魔法</div>
            <div className="text-green-600">帮小动物找到了它们的家</div>
          </motion.div>
          
          <motion.div 
            className="bg-white rounded-2xl p-6 shadow-lg"
            whileHover={{ scale: 1.05, rotate: -2 }}
          >
            <div className="text-5xl mb-3">🧠</div>
            <div className="font-bold text-pink-700 text-xl mb-2">思维魔法</div>
            <div className="text-pink-600">学会了用数学思维看世界</div>
          </motion.div>
        </div>
      </div>
      
      <div className="bg-gradient-to-r from-purple-400 to-pink-400 rounded-2xl p-6 mb-8 text-white">
        <h3 className="text-2xl font-bold mb-3">🌟 记住这个魔法咒语：</h3>
        <p className="text-xl font-medium">
          "数学不只是数字和计算，更是一种理解世界的魔法思维！"
        </p>
      </div>
      
      <div className="flex space-x-6">
        <motion.button
          onClick={() => setCurrentActivity('intro')}
          className="bg-gradient-to-r from-blue-400 to-purple-400 text-white text-2xl font-bold px-8 py-4 rounded-full shadow-xl border-4 border-white"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
        >
          🔄 再玩一次
        </motion.button>
        
        <motion.button
          onClick={() => onComplete && onComplete()}
          className="bg-gradient-to-r from-green-400 to-yellow-400 text-white text-2xl font-bold px-8 py-4 rounded-full shadow-xl border-4 border-white"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
        >
          🚀 下一个冒险
        </motion.button>
      </div>
    </motion.div>
  );

  return (
    <div className="w-full min-h-screen bg-gradient-to-br from-sky-300 via-purple-300 to-pink-300 relative overflow-hidden">
      
      {/* 魔法背景装饰 */}
      <div className="absolute inset-0 pointer-events-none">
        {/* 飘动的魔法粒子 */}
        {Array.from({ length: 20 }, (_, i) => (
          <motion.div
            key={i}
            className="absolute text-2xl"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -30, 0],
              x: [0, Math.random() * 20 - 10, 0],
              rotate: [0, 360],
              opacity: [0.3, 1, 0.3]
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2
            }}
          >
            {['✨', '⭐', '🌟', '💫', '🔮'][Math.floor(Math.random() * 5)]}
          </motion.div>
        ))}
      </div>

      {/* 顶部导航栏 */}
      <div className="relative z-10 bg-gradient-to-r from-purple-500 to-pink-500 p-6 shadow-2xl">
        <div className="flex items-center justify-between max-w-6xl mx-auto">
          <div className="flex items-center space-x-4">
            <motion.div
              className="text-4xl"
              animate={{ rotate: 360 }}
              transition={{ duration: 4, repeat: Infinity }}
            >
              🎭
            </motion.div>
            <div>
              <h1 className="text-3xl font-bold text-white">第1讲：对应与计数</h1>
              <p className="text-purple-100">胡小群数学魔法学院</p>
            </div>
          </div>
          
          {/* 活动导航按钮 */}
          <div className="flex space-x-3">
            {activities.map((activity) => (
              <motion.button
                key={activity.id}
                onClick={() => handleActivityClick(activity.id as ActivityType)}
                className={`w-16 h-16 rounded-full text-2xl font-bold shadow-xl border-4 ${
                  currentActivity === activity.id
                    ? 'border-yellow-300 ring-4 ring-yellow-200'
                    : 'border-white'
                } bg-gradient-to-br ${activity.color} text-white`}
                whileHover={{ scale: 1.1, rotate: [0, -5, 5, 0] }}
                whileTap={{ scale: 0.95 }}
                animate={completedActivities.has(activity.id as ActivityType) ? {
                  boxShadow: ["0 0 0 0 rgba(34, 197, 94, 0.7)", "0 0 0 20px rgba(34, 197, 94, 0)"]
                } : {}}
                transition={{ boxShadow: { duration: 1.5, repeat: Infinity } }}
              >
                {activity.icon}
                {completedActivities.has(activity.id as ActivityType) && (
                  <motion.div
                    className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-sm"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                  >
                    ✓
                  </motion.div>
                )}
              </motion.button>
            ))}
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="relative z-10 p-8">
        <AnimatePresence mode="wait">
          {currentActivity === 'intro' && (
            <motion.div key="intro">
              {renderIntro()}
            </motion.div>
          )}
          
          {currentActivity === 'story' && (
            <motion.div key="story">
              <KidsShepherdAnimation
                onComplete={() => handleActivityComplete('story')}
                onNext={() => setCurrentActivity('game')}
              />
            </motion.div>
          )}
          
          {currentActivity === 'game' && (
            <motion.div key="game">
              <KidsCorrespondenceGame
                onComplete={() => handleActivityComplete('game')}
                onNext={() => setCurrentActivity('summary')}
              />
            </motion.div>
          )}
          
          {currentActivity === 'summary' && (
            <motion.div key="summary">
              {renderSummary()}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default KidsLesson01;
