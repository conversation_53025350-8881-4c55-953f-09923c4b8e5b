import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Lesson, Activity, ActivityResult } from '@/types';
import { lessons as lessonsData } from '@/data/lessons';

// 状态接口
interface LessonState {
  lessons: Lesson[];
  currentLesson: Lesson | null;
  currentActivity: Activity | null;
  loading: boolean;
  error: string | null;
  activityResults: { [activityId: string]: ActivityResult };
  lessonProgress: { [lessonId: string]: number }; // 课程进度百分比
}

// 初始状态
const initialState: LessonState = {
  lessons: lessonsData,
  currentLesson: null,
  currentActivity: null,
  loading: false,
  error: null,
  activityResults: {},
  lessonProgress: {},
};

// 异步actions
export const loadLessons = createAsyncThunk(
  'lesson/loadLessons',
  async (_, { rejectWithValue }) => {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      return lessonsData;
    } catch (error) {
      return rejectWithValue('加载课程失败');
    }
  }
);

export const unlockLesson = createAsyncThunk(
  'lesson/unlockLesson',
  async (lessonId: string, { getState, rejectWithValue }) => {
    try {
      const state = getState() as any;
      const lessons = state.lesson.lessons;
      const lesson = lessons.find((l: Lesson) => l.id === lessonId);
      
      if (!lesson) {
        throw new Error('课程不存在');
      }
      
      // 检查前置条件
      const completedLessons = state.progress.completedLessons || [];
      const canUnlock = lesson.prerequisites.every(prereqId => 
        completedLessons.includes(prereqId)
      );
      
      if (!canUnlock) {
        throw new Error('请先完成前置课程');
      }
      
      return lessonId;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : '解锁课程失败');
    }
  }
);

export const startActivity = createAsyncThunk(
  'lesson/startActivity',
  async ({ lessonId, activityId }: { lessonId: string; activityId: string }, { getState }) => {
    const state = getState() as any;
    const lessons = state.lesson.lessons;
    const lesson = lessons.find((l: Lesson) => l.id === lessonId);
    const activity = lesson?.activities.find((a: Activity) => a.id === activityId);
    
    if (!activity) {
      throw new Error('活动不存在');
    }
    
    return { lesson, activity };
  }
);

export const completeActivity = createAsyncThunk(
  'lesson/completeActivity',
  async (
    { 
      activityId, 
      score, 
      timeSpent, 
      hintsUsed = 0, 
      errors = [] 
    }: {
      activityId: string;
      score: number;
      timeSpent: number;
      hintsUsed?: number;
      errors?: any[];
    },
    { getState }
  ) => {
    const state = getState() as any;
    const currentActivity = state.lesson.currentActivity;
    
    if (!currentActivity || currentActivity.id !== activityId) {
      throw new Error('活动状态不匹配');
    }
    
    const result: ActivityResult = {
      activityId,
      startTime: new Date(Date.now() - timeSpent * 1000),
      endTime: new Date(),
      score,
      attempts: currentActivity.attempts + 1,
      hintsUsed,
      completed: score >= 60, // 60分以上算完成
      timeSpent,
      errors: errors.map(error => ({
        timestamp: new Date(),
        errorType: error.type || 'unknown',
        context: error.context || '',
        userInput: error.userInput,
        correctAnswer: error.correctAnswer,
      })),
    };
    
    return result;
  }
);

// 创建slice
const lessonSlice = createSlice({
  name: 'lesson',
  initialState,
  reducers: {
    setCurrentLesson: (state, action: PayloadAction<string>) => {
      const lesson = state.lessons.find(l => l.id === action.payload);
      state.currentLesson = lesson || null;
      state.currentActivity = null;
    },
    setCurrentActivity: (state, action: PayloadAction<string>) => {
      if (state.currentLesson) {
        const activity = state.currentLesson.activities.find(a => a.id === action.payload);
        state.currentActivity = activity || null;
      }
    },
    incrementActivityAttempts: (state, action: PayloadAction<string>) => {
      if (state.currentActivity && state.currentActivity.id === action.payload) {
        state.currentActivity.attempts += 1;
      }
      
      // 同时更新lessons中的数据
      if (state.currentLesson) {
        const lessonIndex = state.lessons.findIndex(l => l.id === state.currentLesson!.id);
        if (lessonIndex !== -1) {
          const activityIndex = state.lessons[lessonIndex].activities.findIndex(
            a => a.id === action.payload
          );
          if (activityIndex !== -1) {
            state.lessons[lessonIndex].activities[activityIndex].attempts += 1;
          }
        }
      }
    },
    updateLessonProgress: (state, action: PayloadAction<{ lessonId: string; progress: number }>) => {
      state.lessonProgress[action.payload.lessonId] = action.payload.progress;
    },
    resetActivityProgress: (state, action: PayloadAction<string>) => {
      const activityId = action.payload;
      delete state.activityResults[activityId];
      
      // 重置活动状态
      if (state.currentActivity && state.currentActivity.id === activityId) {
        state.currentActivity.completed = false;
        state.currentActivity.attempts = 0;
        state.currentActivity.score = undefined;
      }
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // 加载课程
      .addCase(loadLessons.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loadLessons.fulfilled, (state, action) => {
        state.loading = false;
        state.lessons = action.payload;
        state.error = null;
      })
      .addCase(loadLessons.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // 解锁课程
      .addCase(unlockLesson.fulfilled, (state, action) => {
        const lessonIndex = state.lessons.findIndex(l => l.id === action.payload);
        if (lessonIndex !== -1) {
          state.lessons[lessonIndex].status = 'available';
        }
      })
      .addCase(unlockLesson.rejected, (state, action) => {
        state.error = action.payload as string;
      })
      // 开始活动
      .addCase(startActivity.fulfilled, (state, action) => {
        state.currentLesson = action.payload.lesson;
        state.currentActivity = action.payload.activity;
        state.error = null;
      })
      .addCase(startActivity.rejected, (state, action) => {
        state.error = action.error.message || '开始活动失败';
      })
      // 完成活动
      .addCase(completeActivity.fulfilled, (state, action) => {
        const result = action.payload;
        state.activityResults[result.activityId] = result;
        
        // 更新活动状态
        if (state.currentActivity && state.currentActivity.id === result.activityId) {
          state.currentActivity.completed = result.completed;
          state.currentActivity.score = result.score;
        }
        
        // 更新lessons中的活动状态
        if (state.currentLesson) {
          const lessonIndex = state.lessons.findIndex(l => l.id === state.currentLesson!.id);
          if (lessonIndex !== -1) {
            const activityIndex = state.lessons[lessonIndex].activities.findIndex(
              a => a.id === result.activityId
            );
            if (activityIndex !== -1) {
              state.lessons[lessonIndex].activities[activityIndex].completed = result.completed;
              state.lessons[lessonIndex].activities[activityIndex].score = result.score;
            }
          }
        }
        
        // 计算课程进度
        if (state.currentLesson) {
          const completedActivities = state.currentLesson.activities.filter(a => a.completed).length;
          const totalActivities = state.currentLesson.activities.length;
          const progress = Math.round((completedActivities / totalActivities) * 100);
          state.lessonProgress[state.currentLesson.id] = progress;
        }
      });
  },
});

// 导出actions
export const {
  setCurrentLesson,
  setCurrentActivity,
  incrementActivityAttempts,
  updateLessonProgress,
  resetActivityProgress,
  clearError,
} = lessonSlice.actions;

// 选择器
export const selectLessons = (state: { lesson: LessonState }) => state.lesson.lessons;
export const selectCurrentLesson = (state: { lesson: LessonState }) => state.lesson.currentLesson;
export const selectCurrentActivity = (state: { lesson: LessonState }) => state.lesson.currentActivity;
export const selectLessonLoading = (state: { lesson: LessonState }) => state.lesson.loading;
export const selectLessonError = (state: { lesson: LessonState }) => state.lesson.error;
export const selectActivityResults = (state: { lesson: LessonState }) => state.lesson.activityResults;
export const selectLessonProgress = (state: { lesson: LessonState }) => state.lesson.lessonProgress;

// 复合选择器
export const selectAvailableLessons = (state: { lesson: LessonState }) => 
  state.lesson.lessons.filter(lesson => 
    lesson.status === 'available' || 
    lesson.status === 'in_progress' || 
    lesson.status === 'completed'
  );

export const selectLessonById = (lessonId: string) => (state: { lesson: LessonState }) =>
  state.lesson.lessons.find(lesson => lesson.id === lessonId);

export const selectActivityById = (activityId: string) => (state: { lesson: LessonState }) => {
  for (const lesson of state.lesson.lessons) {
    const activity = lesson.activities.find(a => a.id === activityId);
    if (activity) return activity;
  }
  return null;
};

export default lessonSlice.reducer;