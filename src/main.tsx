import React from 'react'
import ReactDOM from 'react-dom/client'
import { Provider } from 'react-redux'
import { <PERSON>rowserRouter } from 'react-router-dom'
import App from './App'
import { store } from './store'
import './index.css'

// 错误边界组件
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('应用错误:', error, errorInfo);
    // 这里可以添加错误日志上报
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-100">
          <div className="text-center p-8 bg-white rounded-lg shadow-lg max-w-md">
            <h2 className="text-2xl font-bold text-red-600 mb-4">
              哎呀，出错了！
            </h2>
            <p className="text-gray-600 mb-4">
              应用遇到了一个意外错误，请刷新页面重试。
            </p>
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors"
            >
              刷新页面
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// 检查浏览器兼容性
const checkBrowserSupport = () => {
  const unsupportedFeatures = [];
  
  if (!window.localStorage) {
    unsupportedFeatures.push('本地存储');
  }
  
  if (!window.WebGLRenderingContext) {
    unsupportedFeatures.push('WebGL');
  }
  
  if (!window.requestAnimationFrame) {
    unsupportedFeatures.push('动画支持');
  }
  
  if (unsupportedFeatures.length > 0) {
    console.warn('浏览器不支持以下功能:', unsupportedFeatures);
    // 可以显示警告信息或降级处理
  }
};

// 初始化应用
const initializeApp = () => {
  checkBrowserSupport();
  
  // 设置全局错误处理
  window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise拒绝:', event.reason);
    event.preventDefault();
  });
  
  // 初始化性能监控
  if ('performance' in window) {
    window.addEventListener('load', () => {
      setTimeout(() => {
        const perfData = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        console.log('页面加载性能:', {
          loadTime: perfData.loadEventEnd - perfData.loadEventStart,
          domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
          totalTime: perfData.loadEventEnd - perfData.fetchStart
        });
      }, 0);
    });
  }
};

// 启动应用
initializeApp();

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <ErrorBoundary>
      <Provider store={store}>
        <BrowserRouter>
          <App />
        </BrowserRouter>
      </Provider>
    </ErrorBoundary>
  </React.StrictMode>
)