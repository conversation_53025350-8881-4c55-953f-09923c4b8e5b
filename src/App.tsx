import React, { useEffect, Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '@/store';
import { selectIsAuthenticated, selectCurrentUser, refreshSession } from '@/store/slices/authSlice';
import { loadLessons } from '@/store/slices/lessonSlice';

// 懒加载组件
const Login = React.lazy(() => import('@/pages/Login'));
const Dashboard = React.lazy(() => import('@/pages/Dashboard'));
const LessonList = React.lazy(() => import('@/pages/LessonList'));
const LessonDetail = React.lazy(() => import('@/pages/LessonDetail'));
const ActivityPage = React.lazy(() => import('@/pages/ActivityPage'));
const Profile = React.lazy(() => import('@/pages/Profile'));
const Settings = React.lazy(() => import('@/pages/Settings'));
const ParentDashboard = React.lazy(() => import('@/pages/ParentDashboard'));
const Lesson01Demo = React.lazy(() => import('@/pages/Lesson01Demo'));
const KidsLesson01Demo = React.lazy(() => import('@/pages/KidsLesson01Demo'));

// 布局组件
import Layout from '@/components/Layout/Layout';
import LoadingScreen from '@/components/UI/LoadingScreen';
import ErrorBoundary from '@/components/ErrorBoundary/ErrorBoundary';
import NotificationContainer from '@/components/UI/NotificationContainer';

// 受保护的路由组件
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  return <Layout>{children}</Layout>;
};

// 主应用组件
const App: React.FC = () => {
  const dispatch = useAppDispatch();
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const currentUser = useAppSelector(selectCurrentUser);

  // 应用初始化
  useEffect(() => {
    // 检查会话状态
    if (isAuthenticated && currentUser) {
      dispatch(refreshSession());
    }
    
    // 加载课程数据
    dispatch(loadLessons());
    
    // 设置自动会话刷新
    const sessionInterval = setInterval(() => {
      if (isAuthenticated) {
        dispatch(refreshSession());
      }
    }, 15 * 60 * 1000); // 每15分钟刷新一次
    
    return () => clearInterval(sessionInterval);
  }, [dispatch, isAuthenticated, currentUser]);

  // 键盘快捷键处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + K 打开搜索
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        // 触发搜索功能
        console.log('打开搜索');
      }
      
      // ESC 关闭模态框
      if (event.key === 'Escape') {
        // 触发关闭模态框的action
        console.log('关闭模态框');
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  // 在线状态检测
  useEffect(() => {
    const handleOnline = () => {
      console.log('网络连接恢复');
      // 可以触发数据同步
    };

    const handleOffline = () => {
      console.log('网络连接断开');
      // 可以显示离线提示
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return (
    <ErrorBoundary>
      <div className="App min-h-screen bg-gray-50">
        <Suspense fallback={<LoadingScreen />}>
          <Routes>
            {/* 公开路由 */}
            <Route
              path="/login"
              element={
                isAuthenticated ? <Navigate to="/dashboard" replace /> : <Login />
              }
            />

            {/* 演示路由 - 无需登录 */}
            <Route path="/demo/lesson01" element={<Lesson01Demo />} />
            <Route path="/demo/kids-lesson01" element={<KidsLesson01Demo />} />

            {/* 受保护的路由 */}
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
            
            <Route 
              path="/dashboard" 
              element={
                <ProtectedRoute>
                  <Dashboard />
                </ProtectedRoute>
              } 
            />
            
            <Route 
              path="/lessons" 
              element={
                <ProtectedRoute>
                  <LessonList />
                </ProtectedRoute>
              } 
            />
            
            <Route 
              path="/lessons/:lessonId" 
              element={
                <ProtectedRoute>
                  <LessonDetail />
                </ProtectedRoute>
              } 
            />
            
            <Route 
              path="/lessons/:lessonId/activities/:activityId" 
              element={
                <ProtectedRoute>
                  <ActivityPage />
                </ProtectedRoute>
              } 
            />
            
            <Route 
              path="/profile" 
              element={
                <ProtectedRoute>
                  <Profile />
                </ProtectedRoute>
              } 
            />
            
            <Route 
              path="/settings" 
              element={
                <ProtectedRoute>
                  <Settings />
                </ProtectedRoute>
              } 
            />
            
            <Route 
              path="/parent" 
              element={
                <ProtectedRoute>
                  <ParentDashboard />
                </ProtectedRoute>
              } 
            />
            
            {/* 404页面 */}
            <Route 
              path="*" 
              element={
                <div className="min-h-screen flex items-center justify-center bg-gray-100">
                  <div className="text-center">
                    <h1 className="text-6xl font-bold text-gray-400 mb-4">404</h1>
                    <p className="text-xl text-gray-600 mb-8">页面不存在</p>
                    <button
                      onClick={() => window.history.back()}
                      className="btn btn-primary"
                    >
                      返回上一页
                    </button>
                  </div>
                </div>
              } 
            />
          </Routes>
        </Suspense>
        
        {/* 全局通知容器 */}
        <NotificationContainer />
      </div>
    </ErrorBoundary>
  );
};

export default App;