import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo
    });

    // 这里可以添加错误日志上报
    console.error('ErrorBoundary 捕获到错误:', error, errorInfo);
    
    // 上报错误到监控系统
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'exception', {
        description: error.toString(),
        fatal: true
      });
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  handleReload = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      // 如果有自定义的fallback组件，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默认错误页面
      return (
        <div className="min-h-screen bg-gray-100 flex items-center justify-center px-4">
          <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
            {/* 错误图标 */}
            <div className="w-16 h-16 mx-auto mb-6 bg-red-100 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>

            {/* 错误标题 */}
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              哎呀，出错了！
            </h2>

            {/* 错误描述 */}
            <p className="text-gray-600 mb-6">
              应用遇到了一个意外错误。请尝试刷新页面，如果问题仍然存在，请联系技术支持。
            </p>

            {/* 开发环境下显示错误详情 */}
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="text-left mb-6 p-4 bg-gray-50 rounded border">
                <summary className="cursor-pointer font-medium text-gray-700 mb-2">
                  错误详情 (开发环境)
                </summary>
                <div className="text-sm text-gray-600 font-mono">
                  <div className="mb-2">
                    <strong>错误:</strong> {this.state.error.toString()}
                  </div>
                  <div>
                    <strong>组件栈:</strong>
                    <pre className="mt-1 text-xs overflow-auto">
                      {this.state.errorInfo?.componentStack}
                    </pre>
                  </div>
                </div>
              </details>
            )}

            {/* 操作按钮 */}
            <div className="space-y-3">
              <button
                onClick={this.handleRetry}
                className="w-full btn btn-primary"
              >
                重试
              </button>
              
              <button
                onClick={this.handleReload}
                className="w-full btn btn-secondary"
              >
                刷新页面
              </button>
              
              <button
                onClick={() => window.history.back()}
                className="w-full text-gray-500 hover:text-gray-700 transition-colors"
              >
                返回上一页
              </button>
            </div>

            {/* 联系支持 */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <p className="text-sm text-gray-500">
                如果问题持续存在，请{' '}
                <a href="mailto:<EMAIL>" className="text-blue-500 hover:text-blue-600">
                  联系技术支持
                </a>
              </p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;