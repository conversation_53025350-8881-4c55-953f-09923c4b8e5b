import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// 学生数据类型
export interface Student {
  id: string;
  name: string;
  age: number;
  grade: string;
  avatar?: string;
  parentId: string;
  createdAt: string;
  updatedAt: string;
}

// 学习进度类型
export interface LearningProgress {
  studentId: string;
  lessonId: string;
  activityId: string;
  completed: boolean;
  score: number;
  timeSpent: number; // 秒
  attempts: number;
  lastAttemptAt: string;
}

// 学习统计类型
export interface LearningStats {
  totalLessons: number;
  completedLessons: number;
  totalActivities: number;
  completedActivities: number;
  averageScore: number;
  totalTimeSpent: number; // 秒
  streakDays: number;
  lastActiveDate: string;
}

// 状态类型
interface StudentState {
  currentStudent: Student | null;
  students: Student[];
  progress: LearningProgress[];
  stats: LearningStats | null;
  loading: boolean;
  error: string | null;
}

// 初始状态
const initialState: StudentState = {
  currentStudent: null,
  students: [],
  progress: [],
  stats: null,
  loading: false,
  error: null,
};

// 异步操作：获取学生列表
export const fetchStudents = createAsyncThunk(
  'student/fetchStudents',
  async (parentId: string) => {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 返回模拟数据
    const students: Student[] = [
      {
        id: 'student-1',
        name: '小明',
        age: 7,
        grade: '一年级',
        avatar: '/avatars/boy1.png',
        parentId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];
    
    return students;
  }
);

// 异步操作：获取学习进度
export const fetchProgress = createAsyncThunk(
  'student/fetchProgress',
  async (studentId: string) => {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 返回模拟数据
    const progress: LearningProgress[] = [];
    
    return progress;
  }
);

// 异步操作：更新学习进度
export const updateProgress = createAsyncThunk(
  'student/updateProgress',
  async (progressData: Omit<LearningProgress, 'lastAttemptAt'>) => {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const updatedProgress: LearningProgress = {
      ...progressData,
      lastAttemptAt: new Date().toISOString(),
    };
    
    return updatedProgress;
  }
);

// 创建slice
const studentSlice = createSlice({
  name: 'student',
  initialState,
  reducers: {
    setCurrentStudent: (state, action: PayloadAction<Student>) => {
      state.currentStudent = action.payload;
    },
    clearCurrentStudent: (state) => {
      state.currentStudent = null;
    },
    clearError: (state) => {
      state.error = null;
    },
    updateLocalProgress: (state, action: PayloadAction<LearningProgress>) => {
      const existingIndex = state.progress.findIndex(
        p => p.studentId === action.payload.studentId && 
            p.lessonId === action.payload.lessonId && 
            p.activityId === action.payload.activityId
      );
      
      if (existingIndex >= 0) {
        state.progress[existingIndex] = action.payload;
      } else {
        state.progress.push(action.payload);
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // 获取学生列表
      .addCase(fetchStudents.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchStudents.fulfilled, (state, action) => {
        state.loading = false;
        state.students = action.payload;
      })
      .addCase(fetchStudents.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || '获取学生列表失败';
      })
      
      // 获取学习进度
      .addCase(fetchProgress.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProgress.fulfilled, (state, action) => {
        state.loading = false;
        state.progress = action.payload;
      })
      .addCase(fetchProgress.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || '获取学习进度失败';
      })
      
      // 更新学习进度
      .addCase(updateProgress.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateProgress.fulfilled, (state, action) => {
        state.loading = false;
        const existingIndex = state.progress.findIndex(
          p => p.studentId === action.payload.studentId && 
              p.lessonId === action.payload.lessonId && 
              p.activityId === action.payload.activityId
        );
        
        if (existingIndex >= 0) {
          state.progress[existingIndex] = action.payload;
        } else {
          state.progress.push(action.payload);
        }
      })
      .addCase(updateProgress.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || '更新学习进度失败';
      });
  },
});

// 导出actions
export const {
  setCurrentStudent,
  clearCurrentStudent,
  clearError,
  updateLocalProgress,
} = studentSlice.actions;

// 选择器
export const selectCurrentStudent = (state: { student: StudentState }) => state.student.currentStudent;
export const selectStudents = (state: { student: StudentState }) => state.student.students;
export const selectProgress = (state: { student: StudentState }) => state.student.progress;
export const selectStats = (state: { student: StudentState }) => state.student.stats;
export const selectStudentLoading = (state: { student: StudentState }) => state.student.loading;
export const selectStudentError = (state: { student: StudentState }) => state.student.error;

// 导出reducer
export default studentSlice.reducer;
