import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// 进度数据类型
export interface Progress {
  studentId: string;
  lessonId: string;
  activityId: string;
  completed: boolean;
  score: number;
  maxScore: number;
  timeSpent: number; // 秒
  attempts: number;
  startedAt: string;
  completedAt?: string;
  lastAttemptAt: string;
}

// 学习会话类型
export interface StudySession {
  id: string;
  studentId: string;
  startTime: string;
  endTime?: string;
  totalTime: number; // 秒
  lessonsStudied: string[];
  activitiesCompleted: number;
  averageScore: number;
}

// 状态类型
interface ProgressState {
  progresses: Progress[];
  currentSession: StudySession | null;
  loading: boolean;
  error: string | null;
}

// 初始状态
const initialState: ProgressState = {
  progresses: [],
  currentSession: null,
  loading: false,
  error: null,
};

// 创建slice
const progressSlice = createSlice({
  name: 'progress',
  initialState,
  reducers: {
    // 开始学习会话
    startStudySession: (state, action: PayloadAction<{ studentId: string }>) => {
      const session: StudySession = {
        id: `session-${Date.now()}`,
        studentId: action.payload.studentId,
        startTime: new Date().toISOString(),
        totalTime: 0,
        lessonsStudied: [],
        activitiesCompleted: 0,
        averageScore: 0,
      };
      state.currentSession = session;
    },

    // 结束学习会话
    endStudySession: (state) => {
      if (state.currentSession) {
        state.currentSession.endTime = new Date().toISOString();
        state.currentSession.totalTime = Date.now() - new Date(state.currentSession.startTime).getTime();
      }
    },

    // 更新进度
    updateProgress: (state, action: PayloadAction<Omit<Progress, 'lastAttemptAt'>>) => {
      const progressData = {
        ...action.payload,
        lastAttemptAt: new Date().toISOString(),
      };

      const existingIndex = state.progresses.findIndex(
        p => p.studentId === progressData.studentId && 
            p.lessonId === progressData.lessonId && 
            p.activityId === progressData.activityId
      );

      if (existingIndex >= 0) {
        state.progresses[existingIndex] = progressData;
      } else {
        state.progresses.push(progressData);
      }

      // 更新当前会话
      if (state.currentSession && state.currentSession.studentId === progressData.studentId) {
        if (!state.currentSession.lessonsStudied.includes(progressData.lessonId)) {
          state.currentSession.lessonsStudied.push(progressData.lessonId);
        }
        if (progressData.completed) {
          state.currentSession.activitiesCompleted += 1;
        }
        
        // 重新计算平均分
        const completedProgresses = state.progresses.filter(
          p => p.studentId === progressData.studentId && p.completed
        );
        if (completedProgresses.length > 0) {
          state.currentSession.averageScore = 
            completedProgresses.reduce((sum, p) => sum + p.score, 0) / completedProgresses.length;
        }
      }
    },

    // 批量更新进度
    batchUpdateProgress: (state, action: PayloadAction<Progress[]>) => {
      action.payload.forEach(progressData => {
        const existingIndex = state.progresses.findIndex(
          p => p.studentId === progressData.studentId && 
              p.lessonId === progressData.lessonId && 
              p.activityId === progressData.activityId
        );

        if (existingIndex >= 0) {
          state.progresses[existingIndex] = progressData;
        } else {
          state.progresses.push(progressData);
        }
      });
    },

    // 清除进度
    clearProgress: (state, action: PayloadAction<{ studentId: string }>) => {
      state.progresses = state.progresses.filter(
        p => p.studentId !== action.payload.studentId
      );
    },

    // 设置加载状态
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },

    // 设置错误
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },

    // 清除错误
    clearError: (state) => {
      state.error = null;
    },
  },
});

// 导出actions
export const {
  startStudySession,
  endStudySession,
  updateProgress,
  batchUpdateProgress,
  clearProgress,
  setLoading,
  setError,
  clearError,
} = progressSlice.actions;

// 选择器
export const selectProgresses = (state: { progress: ProgressState }) => state.progress.progresses;
export const selectCurrentSession = (state: { progress: ProgressState }) => state.progress.currentSession;
export const selectProgressLoading = (state: { progress: ProgressState }) => state.progress.loading;
export const selectProgressError = (state: { progress: ProgressState }) => state.progress.error;

// 复合选择器
export const selectStudentProgress = (studentId: string) => (state: { progress: ProgressState }) =>
  state.progress.progresses.filter(p => p.studentId === studentId);

export const selectLessonProgress = (studentId: string, lessonId: string) => (state: { progress: ProgressState }) =>
  state.progress.progresses.filter(p => p.studentId === studentId && p.lessonId === lessonId);

export const selectActivityProgress = (studentId: string, lessonId: string, activityId: string) => 
  (state: { progress: ProgressState }) =>
    state.progress.progresses.find(
      p => p.studentId === studentId && p.lessonId === lessonId && p.activityId === activityId
    );

// 导出reducer
export default progressSlice.reducer;
