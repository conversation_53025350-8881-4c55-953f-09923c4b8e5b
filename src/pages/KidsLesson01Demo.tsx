import React from 'react';
import { motion } from 'framer-motion';
import Kids<PERSON>esson01 from '../components/Lessons/Lesson01/KidsLesson01';

const KidsLesson01Demo: React.FC = () => {
  const handleLessonComplete = () => {
    console.log('🎉 Lesson completed!');
    // 这里可以添加完成后的逻辑，比如跳转到下一课
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-400 via-pink-400 to-red-400">
      {/* 魔法背景效果 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* 飘动的魔法元素 */}
        {Array.from({ length: 30 }, (_, i) => (
          <motion.div
            key={i}
            className="absolute text-3xl opacity-20"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -50, 0],
              x: [0, Math.random() * 40 - 20, 0],
              rotate: [0, 360],
              scale: [0.5, 1, 0.5],
              opacity: [0.1, 0.3, 0.1]
            }}
            transition={{
              duration: 4 + Math.random() * 3,
              repeat: Infinity,
              delay: Math.random() * 3,
              ease: "easeInOut"
            }}
          >
            {['🌟', '✨', '⭐', '💫', '🔮', '🎭', '🎨', '🎪', '🎠', '🎡'][Math.floor(Math.random() * 10)]}
          </motion.div>
        ))}
        
        {/* 彩虹背景 */}
        <div className="absolute top-0 left-0 w-full h-32 bg-gradient-to-r from-red-300 via-yellow-300 via-green-300 via-blue-300 to-purple-300 opacity-20 rounded-b-full transform -translate-y-16"></div>
      </div>

      {/* 主要内容 */}
      <div className="relative z-10">
        <KidsLesson01 onComplete={handleLessonComplete} />
      </div>

      {/* 底部装饰 */}
      <div className="fixed bottom-0 left-0 w-full h-16 bg-gradient-to-r from-green-400 via-blue-400 to-purple-400 opacity-30 pointer-events-none"></div>
    </div>
  );
};

export default KidsLesson01Demo;
