import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface KidsCorrespondenceGameProps {
  onComplete: () => void;
  onNext?: () => void;
}

interface Connection {
  from: number;
  to: number;
}

const KidsCorrespondenceGame: React.FC<KidsCorrespondenceGameProps> = ({ onComplete, onNext }) => {
  const [connections, setConnections] = useState<Connection[]>([]);
  const [selectedCircle, setSelectedCircle] = useState<number | null>(null);
  const [score, setScore] = useState(0);
  const [attempts, setAttempts] = useState(0);
  const [showCelebration, setShowCelebration] = useState(false);
  const [gameComplete, setGameComplete] = useState(false);
  const [showHint, setShowHint] = useState(true);

  // 可爱的动物和颜色配对
  const circles = [
    { id: 1, animal: '🐱', color: 'from-pink-400 to-pink-600', name: '小猫咪' },
    { id: 2, animal: '🐶', color: 'from-blue-400 to-blue-600', name: '小狗狗' },
    { id: 3, animal: '🐰', color: 'from-purple-400 to-purple-600', name: '小兔子' },
    { id: 4, animal: '🐸', color: 'from-green-400 to-green-600', name: '小青蛙' },
    { id: 5, animal: '🐻', color: 'from-yellow-400 to-yellow-600', name: '小熊熊' }
  ];

  const triangles = [
    { id: 1, house: '🏠', color: 'from-pink-300 to-pink-500', name: '粉色小屋' },
    { id: 2, house: '🏡', color: 'from-blue-300 to-blue-500', name: '蓝色小屋' },
    { id: 3, house: '🏘️', color: 'from-purple-300 to-purple-500', name: '紫色小屋' },
    { id: 4, house: '🏚️', color: 'from-green-300 to-green-500', name: '绿色小屋' },
    { id: 5, house: '🏰', color: 'from-yellow-300 to-yellow-500', name: '黄色城堡' }
  ];

  useEffect(() => {
    if (connections.length === 5) {
      const correctConnections = connections.every(conn => conn.from === conn.to);
      if (correctConnections) {
        setGameComplete(true);
        setShowCelebration(true);
        setScore(score + 100);
        playSound('success');
        setTimeout(() => {
          onComplete();
        }, 3000);
      }
    }
  }, [connections]);

  useEffect(() => {
    // 5秒后隐藏提示
    const timer = setTimeout(() => {
      setShowHint(false);
    }, 5000);
    return () => clearTimeout(timer);
  }, []);

  const playSound = (type: 'connect' | 'success' | 'error') => {
    console.log(`🔊 Playing ${type} sound!`);
  };

  const handleCircleClick = (circleId: number) => {
    if (gameComplete) return;
    
    if (selectedCircle === circleId) {
      setSelectedCircle(null);
      return;
    }
    
    if (selectedCircle === null) {
      setSelectedCircle(circleId);
      playSound('connect');
    } else {
      // 检查是否已经有连接
      const existingConnection = connections.find(conn => conn.from === selectedCircle);
      if (existingConnection) {
        // 替换现有连接
        setConnections(prev => prev.map(conn => 
          conn.from === selectedCircle ? { from: selectedCircle, to: circleId } : conn
        ));
      } else {
        // 添加新连接
        setConnections(prev => [...prev, { from: selectedCircle, to: circleId }]);
      }
      
      setAttempts(prev => prev + 1);
      
      if (selectedCircle === circleId) {
        setScore(prev => prev + 20);
        playSound('success');
      } else {
        playSound('error');
      }
      
      setSelectedCircle(null);
    }
  };

  const handleReset = () => {
    setConnections([]);
    setSelectedCircle(null);
    setScore(0);
    setAttempts(0);
    setShowCelebration(false);
    setGameComplete(false);
    setShowHint(true);
  };

  const getConnectionPath = (from: number, to: number) => {
    const fromX = 150;
    const fromY = 100 + (from - 1) * 80;
    const toX = 450;
    const toY = 100 + (to - 1) * 80;
    
    return `M ${fromX} ${fromY} Q ${(fromX + toX) / 2} ${(fromY + toY) / 2 - 30} ${toX} ${toY}`;
  };

  return (
    <div className="w-full min-h-[700px] bg-gradient-to-br from-sky-200 via-purple-200 to-pink-200 rounded-3xl relative overflow-hidden shadow-2xl border-8 border-rainbow">
      
      {/* 魔法背景装饰 */}
      <div className="absolute inset-0">
        {/* 彩虹 */}
        <div className="absolute top-4 left-1/2 transform -translate-x-1/2 w-80 h-20 opacity-40">
          <div className="w-full h-full rounded-t-full bg-gradient-to-r from-red-300 via-yellow-300 via-green-300 via-blue-300 to-purple-300"></div>
        </div>
        
        {/* 飘动的心形 */}
        <motion.div 
          className="absolute top-12 left-12 text-3xl"
          animate={{ y: [0, -20, 0], rotate: [0, 10, 0] }}
          transition={{ duration: 3, repeat: Infinity }}
        >
          💖
        </motion.div>
        <motion.div 
          className="absolute top-20 right-16 text-2xl"
          animate={{ y: [0, -15, 0], rotate: [0, -10, 0] }}
          transition={{ duration: 2.5, repeat: Infinity }}
        >
          💝
        </motion.div>
        
        {/* 闪烁的星星 */}
        <motion.div 
          className="absolute top-32 left-1/3 text-2xl"
          animate={{ scale: [1, 1.5, 1], opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          ⭐
        </motion.div>
      </div>

      {/* 游戏标题 */}
      <div className="absolute top-8 left-1/2 transform -translate-x-1/2 text-center">
        <motion.h2 
          className="text-4xl font-bold text-purple-800 mb-2"
          animate={{ scale: [1, 1.05, 1] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          🌟 帮小动物找到家 🏠
        </motion.h2>
        <p className="text-xl text-purple-600 font-medium">
          点击小动物，再点击它们的家，帮它们回家吧！
        </p>
      </div>

      {/* 提示动画 */}
      <AnimatePresence>
        {showHint && (
          <motion.div
            className="absolute top-32 left-1/2 transform -translate-x-1/2 bg-yellow-300 text-yellow-800 px-6 py-3 rounded-full text-lg font-bold shadow-lg border-4 border-yellow-400"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0, scale: [1, 1.1, 1] }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ scale: { duration: 1, repeat: Infinity } }}
          >
            👆 先点小动物，再点它的家！
          </motion.div>
        )}
      </AnimatePresence>

      {/* 左侧：可爱的小动物 */}
      <div className="absolute left-16 top-48 space-y-6">
        <h3 className="text-2xl font-bold text-center text-purple-800 mb-4">🐾 小动物们</h3>
        {circles.map((circle, index) => (
          <motion.div
            key={circle.id}
            className={`w-24 h-24 rounded-full bg-gradient-to-br ${circle.color} flex flex-col items-center justify-center cursor-pointer shadow-xl border-4 ${
              selectedCircle === circle.id ? 'border-yellow-400 ring-4 ring-yellow-300' : 'border-white'
            }`}
            whileHover={{ scale: 1.1, rotate: [0, -5, 5, 0] }}
            whileTap={{ scale: 0.95 }}
            animate={{ 
              y: Math.sin(Date.now() / 1000 + index) * 5,
              scale: selectedCircle === circle.id ? [1, 1.1, 1] : 1
            }}
            transition={{ 
              y: { duration: 2, repeat: Infinity },
              scale: { duration: 0.5, repeat: Infinity }
            }}
            onClick={() => handleCircleClick(circle.id)}
          >
            <div className="text-3xl mb-1">{circle.animal}</div>
            <div className="text-xs text-white font-bold text-center">{circle.name}</div>
          </motion.div>
        ))}
      </div>

      {/* 右侧：可爱的小房子 */}
      <div className="absolute right-16 top-48 space-y-6">
        <h3 className="text-2xl font-bold text-center text-purple-800 mb-4">🏠 小房子们</h3>
        {triangles.map((triangle, index) => (
          <motion.div
            key={triangle.id}
            className={`w-24 h-24 rounded-2xl bg-gradient-to-br ${triangle.color} flex flex-col items-center justify-center cursor-pointer shadow-xl border-4 border-white`}
            whileHover={{ scale: 1.1, rotate: [0, 3, -3, 0] }}
            whileTap={{ scale: 0.95 }}
            animate={{ 
              y: Math.sin(Date.now() / 1000 + index + Math.PI) * 5,
            }}
            transition={{ 
              y: { duration: 2, repeat: Infinity }
            }}
            onClick={() => selectedCircle && handleCircleClick(triangle.id)}
          >
            <div className="text-3xl mb-1">{triangle.house}</div>
            <div className="text-xs text-white font-bold text-center">{triangle.name}</div>
          </motion.div>
        ))}
      </div>

      {/* SVG连线 */}
      <svg className="absolute inset-0 w-full h-full pointer-events-none">
        {connections.map((connection, index) => (
          <motion.path
            key={`${connection.from}-${connection.to}`}
            d={getConnectionPath(connection.from, connection.to)}
            stroke={connection.from === connection.to ? "#10B981" : "#EF4444"}
            strokeWidth="6"
            fill="none"
            strokeLinecap="round"
            strokeDasharray="10,5"
            initial={{ pathLength: 0 }}
            animate={{ pathLength: 1 }}
            transition={{ duration: 0.8, delay: index * 0.1 }}
          />
        ))}
      </svg>

      {/* 得分面板 */}
      <div className="absolute top-8 left-8 bg-gradient-to-br from-green-400 to-green-600 rounded-2xl p-4 shadow-xl border-4 border-green-300">
        <div className="text-white text-center">
          <div className="text-2xl font-bold mb-2">🏆 得分</div>
          <div className="text-3xl font-bold">{score}</div>
          <div className="text-sm mt-2">尝试次数: {attempts}</div>
        </div>
      </div>

      {/* 进度条 */}
      <div className="absolute top-8 right-8 bg-white rounded-2xl p-4 shadow-xl border-4 border-purple-300">
        <div className="text-purple-800 text-center mb-2 font-bold">进度</div>
        <div className="w-32 h-4 bg-gray-200 rounded-full overflow-hidden">
          <motion.div
            className="h-full bg-gradient-to-r from-purple-400 to-pink-400"
            initial={{ width: 0 }}
            animate={{ width: `${(connections.length / 5) * 100}%` }}
            transition={{ duration: 0.5 }}
          />
        </div>
        <div className="text-sm text-purple-600 mt-1 text-center">
          {connections.length}/5 完成
        </div>
      </div>

      {/* 庆祝动画 */}
      <AnimatePresence>
        {showCelebration && (
          <motion.div
            className="absolute inset-0 flex items-center justify-center pointer-events-none"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="text-9xl"
              animate={{ 
                scale: [0, 1.5, 1],
                rotate: [0, 360, 720]
              }}
              transition={{ duration: 2 }}
            >
              🎉
            </motion.div>
            
            <motion.div
              className="absolute text-6xl text-yellow-400"
              animate={{ 
                scale: [0, 1, 0],
                y: [0, -100, -200]
              }}
              transition={{ duration: 2, delay: 0.5 }}
            >
              ⭐
            </motion.div>
            
            {/* 彩带效果 */}
            {Array.from({ length: 12 }, (_, i) => (
              <motion.div
                key={i}
                className="absolute w-2 h-16 bg-gradient-to-b from-pink-400 to-purple-400 rounded-full"
                initial={{ 
                  x: 0, 
                  y: 0, 
                  rotate: i * 30,
                  scale: 0 
                }}
                animate={{ 
                  x: Math.cos(i * 30 * Math.PI / 180) * 300,
                  y: Math.sin(i * 30 * Math.PI / 180) * 300,
                  scale: 1,
                  rotate: i * 30 + 360
                }}
                transition={{ 
                  duration: 2,
                  delay: i * 0.1
                }}
              />
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* 控制按钮 */}
      <div className="absolute bottom-8 right-8 flex space-x-4">
        <motion.button
          onClick={handleReset}
          className="w-16 h-16 rounded-full bg-gradient-to-br from-orange-400 to-orange-600 text-white text-2xl font-bold shadow-xl border-4 border-orange-300"
          whileHover={{ scale: 1.1, rotate: [0, -10, 10, 0] }}
          whileTap={{ scale: 0.95 }}
        >
          🔄
        </motion.button>
        
        {gameComplete && (
          <motion.button
            onClick={() => onNext && onNext()}
            className="w-16 h-16 rounded-full bg-gradient-to-br from-green-400 to-green-600 text-white text-2xl font-bold shadow-xl border-4 border-green-300"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            whileHover={{ scale: 1.1, x: [0, 5, 0] }}
            whileTap={{ scale: 0.95 }}
          >
            ➡️
          </motion.button>
        )}
      </div>

      {/* 完成消息 */}
      {gameComplete && (
        <motion.div
          className="absolute bottom-24 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-green-400 to-blue-400 text-white px-8 py-4 rounded-2xl text-xl font-bold shadow-xl border-4 border-white"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
        >
          🎊 太棒了！所有小动物都找到家了！ 🎊
        </motion.div>
      )}
    </div>
  );
};

export default KidsCorrespondenceGame;
