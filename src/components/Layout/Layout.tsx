import React from 'react';
import { useAppSelector } from '@/store';
import { selectSidebar } from '@/store/slices/uiSlice';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const sidebar = useAppSelector(selectSidebar);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 主要内容区域 */}
      <main className="flex-1">
        {children}
      </main>
    </div>
  );
};

export default Layout;
