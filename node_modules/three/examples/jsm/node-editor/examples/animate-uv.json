{"objects": {"733": {"x": 1718, "y": 136, "width": 300, "elements": [734, 736, 737, 738, 739], "id": 733, "type": "StandardMaterialEditor"}, "734": {"outputLength": 1, "style": "blue", "title": "Standard Material", "id": 734, "type": "TitleElement"}, "736": {"inputLength": 3, "inputs": [740], "links": [809], "label": "Color", "id": 736, "type": "LabelElement"}, "737": {"inputLength": 1, "inputs": [741], "label": "Opacity", "icon": "ti ti-layers-subtract", "id": 737, "type": "LabelElement"}, "738": {"inputLength": 1, "inputs": [743], "label": "Metalness", "id": 738, "type": "LabelElement"}, "739": {"inputLength": 1, "inputs": [745], "label": "Roughness", "id": 739, "type": "LabelElement"}, "740": {"value": 16777215, "id": 740, "type": "ColorInput"}, "741": {"min": 0, "max": 1, "value": 1, "id": 741, "type": "SliderInput"}, "743": {"min": 0, "max": 1, "value": 0, "id": 743, "type": "SliderInput"}, "745": {"min": 0, "max": 1, "value": 1, "id": 745, "type": "SliderInput"}, "752": {"x": 155, "y": 230, "width": 250, "elements": [753, 759, 760, 757], "id": 752, "type": "Timer<PERSON><PERSON>or"}, "753": {"outputLength": 1, "title": "Timer", "icon": "ti ti-clock", "id": 753, "type": "TitleElement"}, "755": {"value": 0.218, "id": 755, "type": "NumberInput"}, "756": {"value": 0.04, "id": 756, "type": "NumberInput"}, "757": {"inputs": [758], "id": 757, "type": "Element"}, "758": {"value": "Reset", "id": 758, "type": "ButtonInput"}, "759": {"inputs": [755], "id": 759, "type": "Element"}, "760": {"inputs": [756], "label": "Scale", "id": 760, "type": "LabelElement"}, "768": {"x": 202, "y": 79, "width": 250, "elements": [769, 772], "id": 768, "type": "UVEditor"}, "769": {"outputLength": 2, "style": "red", "title": "UV", "id": 769, "type": "TitleElement"}, "771": {"options": ["1", "2"], "value": "0", "id": 771, "type": "SelectInput"}, "772": {"inputs": [771], "label": "Channel", "id": 772, "type": "LabelElement"}, "776": {"x": 612, "y": 102, "width": 250, "elements": [777, 782, 780, 781], "id": 776, "type": "OperatorEditor"}, "777": {"outputLength": 1, "title": "Operator", "id": 777, "type": "TitleElement"}, "779": {"options": [{"name": "+ Addition", "value": "+"}, {"name": "- Subtraction", "value": "-"}, {"name": "* Multiplication", "value": "*"}, {"name": "/ Division", "value": "/"}], "value": "+", "id": 779, "type": "SelectInput"}, "780": {"inputLength": 3, "links": [769], "label": "A", "id": 780, "type": "LabelElement"}, "781": {"inputLength": 3, "links": [753], "label": "B", "id": 781, "type": "LabelElement"}, "782": {"inputs": [779], "id": 782, "type": "Element"}, "788": {"x": 1047, "y": 158, "width": 250, "elements": [789, 794, 792, 793], "id": 788, "type": "OperatorEditor"}, "789": {"outputLength": 1, "title": "Operator", "id": 789, "type": "TitleElement"}, "791": {"options": [{"name": "+ Addition", "value": "+"}, {"name": "- Subtraction", "value": "-"}, {"name": "* Multiplication", "value": "*"}, {"name": "/ Division", "value": "/"}], "value": "*", "id": 791, "type": "SelectInput"}, "792": {"inputLength": 3, "links": [777], "label": "A", "id": 792, "type": "LabelElement"}, "793": {"inputLength": 3, "links": [801], "label": "B", "id": 793, "type": "LabelElement"}, "794": {"inputs": [791], "id": 794, "type": "Element"}, "800": {"x": 601, "y": 345, "width": 250, "elements": [801, 804], "id": 800, "type": "FloatEditor"}, "801": {"outputLength": 1, "title": "Float", "icon": "ti ti-box-multiple-1", "id": 801, "type": "TitleElement"}, "803": {"value": 24.12, "id": 803, "type": "NumberInput"}, "804": {"inputs": [803], "id": 804, "type": "Element"}, "808": {"x": 1402, "y": 14, "width": 200, "elements": [809, 811], "id": 808, "type": "CheckerEditor"}, "809": {"outputLength": 1, "title": "Checker", "id": 809, "type": "TitleElement"}, "811": {"inputLength": 2, "links": [789], "label": "UV", "id": 811, "type": "LabelElement"}, "837": {"x": 2160, "y": 128, "width": 300, "elements": [838, 841, 851, 852, 853, 854], "id": 837, "type": "MeshEditor"}, "838": {"outputLength": 1, "title": "<PERSON><PERSON>", "id": 838, "type": "TitleElement"}, "840": {"value": "<PERSON><PERSON><PERSON>", "id": 840, "type": "StringInput"}, "841": {"inputs": [840], "label": "Name", "id": 841, "type": "LabelElement"}, "842": {"value": 0, "id": 842, "type": "NumberInput"}, "843": {"value": 0, "id": 843, "type": "NumberInput"}, "844": {"value": 10, "id": 844, "type": "NumberInput"}, "845": {"value": 0, "id": 845, "type": "NumberInput"}, "846": {"value": 0, "id": 846, "type": "NumberInput"}, "847": {"value": 0, "id": 847, "type": "NumberInput"}, "848": {"value": 100, "id": 848, "type": "NumberInput"}, "849": {"value": 100, "id": 849, "type": "NumberInput"}, "850": {"value": 100, "id": 850, "type": "NumberInput"}, "851": {"inputs": [842, 843, 844], "label": "Position", "id": 851, "type": "LabelElement"}, "852": {"inputs": [845, 846, 847], "label": "Rotation", "id": 852, "type": "LabelElement"}, "853": {"inputs": [848, 849, 850], "label": "Scale", "id": 853, "type": "LabelElement"}, "854": {"inputLength": 1, "links": [734], "label": "Material", "id": 854, "type": "LabelElement"}}, "nodes": [733, 752, 768, 776, 788, 800, 808, 837], "id": 2, "type": "<PERSON><PERSON>"}