import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '@/store';
import { loginStudent, selectAuthLoading, selectAuthError, clearError } from '@/store/slices/authSlice';

const Login: React.FC = () => {
  const [username, setUsername] = useState('');
  const [showWelcome, setShowWelcome] = useState(true);
  const [selectedAvatar, setSelectedAvatar] = useState('/avatars/boy1.png');
  
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const loading = useAppSelector(selectAuthLoading);
  const error = useAppSelector(selectAuthError);

  // 预设的头像选项
  const avatars = [
    { id: 'boy1', src: '/avatars/boy1.png', label: '小男孩1' },
    { id: 'girl1', src: '/avatars/girl1.png', label: '小女孩1' },
    { id: 'boy2', src: '/avatars/boy2.png', label: '小男孩2' },
    { id: 'girl2', src: '/avatars/girl2.png', label: '小女孩2' },
    { id: 'panda', src: '/avatars/panda.png', label: '熊猫' },
    { id: 'rabbit', src: '/avatars/rabbit.png', label: '兔子' },
  ];

  useEffect(() => {
    // 清除之前的错误
    dispatch(clearError());
    
    // 欢迎动画
    const timer = setTimeout(() => setShowWelcome(false), 2000);
    return () => clearTimeout(timer);
  }, [dispatch]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!username.trim()) {
      return;
    }

    try {
      await dispatch(loginStudent({ username: username.trim() })).unwrap();
      navigate('/dashboard');
    } catch (error) {
      console.error('登录失败:', error);
    }
  };

  if (showWelcome) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500 flex items-center justify-center">
        <div className="text-center text-white animate-fade-in">
          <div className="mb-8">
            <h1 className="text-6xl font-bold mb-4 animate-bounce">🧮</h1>
            <h2 className="text-4xl font-bold mb-2">胡小群数学学习平台</h2>
            <p className="text-xl opacity-90">让数学学习更有趣</p>
          </div>
          
          <div className="flex justify-center space-x-4 text-5xl animate-pulse">
            <span className="animate-bounce">+</span>
            <span className="animate-bounce animation-delay-200">-</span>
            <span className="animate-bounce animation-delay-400">×</span>
            <span className="animate-bounce animation-delay-600">÷</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500 flex items-center justify-center px-4">
      <div className="max-w-md w-full">
        {/* 登录卡片 */}
        <div className="bg-white rounded-2xl shadow-2xl p-8 animate-slide-in-bottom">
          {/* Logo和标题 */}
          <div className="text-center mb-8">
            <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-3xl text-white font-bold mx-auto mb-4">
              🧮
            </div>
            <h1 className="text-2xl font-bold text-gray-800 mb-2">
              欢迎来到数学世界
            </h1>
            <p className="text-gray-600">
              请输入你的名字开始学习吧！
            </p>
          </div>

          {/* 登录表单 */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* 头像选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                选择你的头像
              </label>
              <div className="grid grid-cols-3 gap-3">
                {avatars.map((avatar) => (
                  <button
                    key={avatar.id}
                    type="button"
                    onClick={() => setSelectedAvatar(avatar.src)}
                    className={`w-16 h-16 rounded-full border-4 transition-all hover:scale-110 ${
                      selectedAvatar === avatar.src
                        ? 'border-blue-500 ring-2 ring-blue-200'
                        : 'border-gray-200 hover:border-blue-300'
                    }`}
                  >
                    <img
                      src={avatar.src}
                      alt={avatar.label}
                      className="w-full h-full rounded-full object-cover"
                    />
                  </button>
                ))}
              </div>
            </div>

            {/* 姓名输入 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                你的名字
              </label>
              <input
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="input text-lg"
                placeholder="请输入你的名字"
                maxLength={20}
                required
              />
              {username && (
                <p className="mt-2 text-sm text-gray-500">
                  你好，{username}！👋
                </p>
              )}
            </div>

            {/* 错误提示 */}
            {error && (
              <div className="feedback-error animate-shake">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <span>{error}</span>
              </div>
            )}

            {/* 登录按钮 */}
            <button
              type="submit"
              disabled={!username.trim() || loading}
              className="w-full btn btn-primary text-lg py-4 relative"
            >
              {loading ? (
                <div className="flex items-center justify-center">
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  正在进入...
                </div>
              ) : (
                <div className="flex items-center justify-center">
                  <span>开始学习之旅</span>
                  <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </div>
              )}
            </button>
          </form>

          {/* 特色介绍 */}
          <div className="mt-8 pt-6 border-t border-gray-100">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="space-y-2">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                  🎮
                </div>
                <p className="text-xs text-gray-600">趣味游戏</p>
              </div>
              <div className="space-y-2">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                  🏆
                </div>
                <p className="text-xs text-gray-600">成就系统</p>
              </div>
              <div className="space-y-2">
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
                  📊
                </div>
                <p className="text-xs text-gray-600">学习追踪</p>
              </div>
            </div>
          </div>
        </div>

        {/* 版权信息 */}
        <div className="text-center mt-6 text-white text-sm opacity-80">
          <p>基于胡小群老师教育理念设计</p>
          <p className="mt-1">© 2024 数学学习平台</p>
        </div>
      </div>
    </div>
  );
};

export default Login;