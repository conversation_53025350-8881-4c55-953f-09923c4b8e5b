import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import ShepherdAni<PERSON> from './ShepherdAnimation';
import CorrespondenceGame from './CorrespondenceGame';
import ChildrenChairGame from './ChildrenChairGame';

interface Lesson01Props {
  onComplete: (lessonId: string, scores: number[]) => void;
}

type ActivityType = 'intro' | 'shepherd' | 'correspondence' | 'children-chair' | 'summary';

const Lesson01: React.FC<Lesson01Props> = ({ onComplete }) => {
  const [currentActivity, setCurrentActivity] = useState<ActivityType>('intro');
  const [completedActivities, setCompletedActivities] = useState<Set<ActivityType>>(new Set());
  const [scores, setScores] = useState<number[]>([]);
  const [showIntro, setShowIntro] = useState(true);

  const activities = [
    { id: 'intro', title: '课程介绍', icon: '📚' },
    { id: 'shepherd', title: '牧羊人的故事', icon: '🐑' },
    { id: 'correspondence', title: '连线游戏', icon: '🔗' },
    { id: 'children-chair', title: '孩子与椅子', icon: '🪑' },
    { id: 'summary', title: '课程总结', icon: '🎯' }
  ];

  const handleActivityComplete = (score: number) => {
    setScores(prev => [...prev, score]);
    setCompletedActivities(prev => new Set([...prev, currentActivity]));
    
    // 自动进入下一个活动
    const currentIndex = activities.findIndex(a => a.id === currentActivity);
    if (currentIndex < activities.length - 1) {
      setCurrentActivity(activities[currentIndex + 1].id as ActivityType);
    }
  };

  const handleLessonComplete = () => {
    const allCompleted = activities.slice(1, -1).every(a => completedActivities.has(a.id as ActivityType));
    if (allCompleted) {
      onComplete('lesson01', scores);
    }
  };

  const navigateToActivity = (activityId: ActivityType) => {
    setCurrentActivity(activityId);
  };

  const renderCurrentActivity = () => {
    switch (currentActivity) {
      case 'intro':
        return (
          <div className="h-full flex items-center justify-center">
            <div className="max-w-2xl mx-auto text-center p-8 bg-white rounded-2xl shadow-lg">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <h1 className="text-4xl font-bold text-blue-800 mb-6">第1讲：对应与计数</h1>
                
                <div className="text-6xl mb-6">🔢</div>
                
                <div className="text-lg text-gray-700 leading-relaxed mb-8">
                  <p className="mb-4">
                    欢迎来到数学世界的第一课！今天我们要学习一个非常重要的数学概念——<span className="font-bold text-blue-600">对应</span>。
                  </p>
                  <p className="mb-4">
                    通过有趣的故事和游戏，你将发现数学在生活中无处不在，
                    并且学会用对应的方法来理解数字的真正含义。
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="text-3xl mb-2">🐑</div>
                    <h3 className="font-bold text-blue-800 mb-2">牧羊人的智慧</h3>
                    <p className="text-sm text-blue-600">了解古代牧羊人如何用石头计数</p>
                  </div>
                  
                  <div className="bg-purple-50 p-4 rounded-lg">
                    <div className="text-3xl mb-2">🔗</div>
                    <h3 className="font-bold text-purple-800 mb-2">连线配对</h3>
                    <p className="text-sm text-purple-600">通过游戏学习一一对应关系</p>
                  </div>
                  
                  <div className="bg-orange-50 p-4 rounded-lg">
                    <div className="text-3xl mb-2">🪑</div>
                    <h3 className="font-bold text-orange-800 mb-2">生活实例</h3>
                    <p className="text-sm text-orange-600">孩子与椅子的对应关系</p>
                  </div>
                </div>

                <motion.button
                  onClick={() => setCurrentActivity('shepherd')}
                  className="w-full py-4 bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xl font-bold rounded-lg hover:from-blue-600 hover:to-purple-600 transition-all duration-300"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  开始学习之旅 ✨
                </motion.button>
              </motion.div>
            </div>
          </div>
        );

      case 'shepherd':
        return (
          <ShepherdAnimation 
            onComplete={() => handleActivityComplete(100)}
            onNext={() => setCurrentActivity('correspondence')}
          />
        );

      case 'correspondence':
        return (
          <CorrespondenceGame 
            onComplete={handleActivityComplete}
            onNext={() => setCurrentActivity('children-chair')}
          />
        );

      case 'children-chair':
        return (
          <ChildrenChairGame 
            onComplete={handleActivityComplete}
            onNext={() => setCurrentActivity('summary')}
          />
        );

      case 'summary':
        return (
          <div className="h-full flex items-center justify-center">
            <div className="max-w-2xl mx-auto text-center p-8 bg-white rounded-2xl shadow-lg">
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6 }}
              >
                <div className="text-6xl mb-6">🎉</div>
                <h2 className="text-3xl font-bold text-green-800 mb-6">恭喜完成第1讲！</h2>
                
                <div className="bg-green-50 p-6 rounded-lg mb-6">
                  <h3 className="text-xl font-bold text-green-800 mb-4">你学到了什么？</h3>
                  <div className="text-left space-y-3 text-green-700">
                    <div className="flex items-start space-x-3">
                      <span className="text-green-500 font-bold">✓</span>
                      <span>理解了对应关系的重要性</span>
                    </div>
                    <div className="flex items-start space-x-3">
                      <span className="text-green-500 font-bold">✓</span>
                      <span>学会了一一对应的概念</span>
                    </div>
                    <div className="flex items-start space-x-3">
                      <span className="text-green-500 font-bold">✓</span>
                      <span>发现了数学在生活中的应用</span>
                    </div>
                    <div className="flex items-start space-x-3">
                      <span className="text-green-500 font-bold">✓</span>
                      <span>掌握了不用数字也能计数的方法</span>
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 p-4 rounded-lg mb-6">
                  <h4 className="font-bold text-blue-800 mb-2">你的成绩</h4>
                  <div className="flex justify-center space-x-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{scores.length}</div>
                      <div className="text-sm text-blue-500">完成活动</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {scores.length > 0 ? Math.round(scores.reduce((a, b) => a + b, 0) / scores.length) : 0}
                      </div>
                      <div className="text-sm text-blue-500">平均得分</div>
                    </div>
                  </div>
                </div>

                <div className="text-gray-600 mb-6">
                  <p className="mb-2">记住：数学不只是数字和计算，</p>
                  <p>更是一种理解世界的思维方式！</p>
                </div>

                <motion.button
                  onClick={handleLessonComplete}
                  className="w-full py-4 bg-gradient-to-r from-green-500 to-blue-500 text-white text-xl font-bold rounded-lg hover:from-green-600 hover:to-blue-600 transition-all duration-300"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  继续下一讲 🚀
                </motion.button>
              </motion.div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50">
      {/* 顶部导航栏 */}
      <div className="bg-white shadow-sm border-b sticky top-0 z-10">
        <div className="max-w-6xl mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-bold text-gray-800">第1讲：对应与计数</h1>
            </div>
            
            {/* 进度指示器 */}
            <div className="flex items-center space-x-2">
              {activities.map((activity, index) => (
                <div key={activity.id} className="flex items-center">
                  <motion.button
                    onClick={() => navigateToActivity(activity.id as ActivityType)}
                    className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold transition-all ${
                      currentActivity === activity.id
                        ? 'bg-blue-500 text-white scale-110'
                        : completedActivities.has(activity.id as ActivityType)
                        ? 'bg-green-500 text-white'
                        : 'bg-gray-200 text-gray-500'
                    }`}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {completedActivities.has(activity.id as ActivityType) ? '✓' : activity.icon}
                  </motion.button>
                  
                  {index < activities.length - 1 && (
                    <div className={`w-8 h-1 mx-1 rounded ${
                      completedActivities.has(activity.id as ActivityType) ? 'bg-green-300' : 'bg-gray-200'
                    }`} />
                  )}
                </div>
              ))}
            </div>
          </div>
          
          {/* 活动标题 */}
          <div className="mt-2 text-sm text-gray-600">
            当前活动: {activities.find(a => a.id === currentActivity)?.title}
          </div>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="max-w-6xl mx-auto px-4 py-6">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentActivity}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="min-h-96"
          >
            {renderCurrentActivity()}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default Lesson01;