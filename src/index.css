@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义CSS变量 */
:root {
  --primary-color: #3b82f6;
  --secondary-color: #d946ef;
  --success-color: #22c55e;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --background-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --card-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  --border-radius: 12px;
  --animation-duration: 0.3s;
}

/* 基础样式重置 */
@layer base {
  * {
    box-sizing: border-box;
  }
  
  html {
    scroll-behavior: smooth;
  }
  
  body {
    font-family: 'Noto Sans SC', 'Comic Neue', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background: #f8fafc;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
  }
  
  button {
    cursor: pointer;
    user-select: none;
  }
  
  /* 禁用选择 */
  .no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
}

/* 通用组件样式 */
@layer components {
  /* 卡片样式 */
  .card {
    @apply bg-white rounded-xl shadow-lg border border-gray-100 transition-all duration-300;
  }
  
  .card:hover {
    @apply shadow-xl transform -translate-y-1;
  }
  
  /* 按钮样式 */
  .btn {
    @apply px-6 py-3 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500 shadow-lg;
  }
  
  .btn-secondary {
    @apply bg-gray-200 text-gray-800 hover:bg-gray-300 focus:ring-gray-500;
  }
  
  .btn-success {
    @apply bg-success-500 text-white hover:bg-success-600 focus:ring-success-500;
  }
  
  .btn-warning {
    @apply bg-warning-500 text-white hover:bg-warning-600 focus:ring-warning-500;
  }
  
  .btn-danger {
    @apply bg-red-500 text-white hover:bg-red-600 focus:ring-red-500;
  }
  
  /* 输入框样式 */
  .input {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200;
  }
  
  /* 数学公式样式 */
  .math-expression {
    @apply font-math text-xl text-center my-4 p-4 bg-blue-50 rounded-lg border-2 border-blue-200;
  }
  
  /* 学习进度条 */
  .progress-bar {
    @apply w-full h-3 bg-gray-200 rounded-full overflow-hidden;
  }
  
  .progress-fill {
    @apply h-full bg-gradient-to-r from-primary-400 to-primary-600 transition-all duration-1000 ease-out;
  }
  
  /* 成就徽章 */
  .achievement-badge {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 border border-yellow-300;
  }
  
  /* 反馈消息 */
  .feedback-success {
    @apply bg-green-100 border border-green-300 text-green-800 px-4 py-3 rounded-lg flex items-center space-x-2;
  }
  
  .feedback-error {
    @apply bg-red-100 border border-red-300 text-red-800 px-4 py-3 rounded-lg flex items-center space-x-2;
  }
  
  .feedback-info {
    @apply bg-blue-100 border border-blue-300 text-blue-800 px-4 py-3 rounded-lg flex items-center space-x-2;
  }
}

/* 实用工具类 */
@layer utilities {
  /* 渐变背景 */
  .gradient-bg {
    background: var(--background-gradient);
  }
  
  /* 玻璃效果 */
  .glass {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
  
  /* 脉冲动画 */
  .pulse-ring {
    animation: pulse-ring 2s infinite;
  }
  
  @keyframes pulse-ring {
    0% {
      transform: scale(0.8);
      opacity: 1;
    }
    100% {
      transform: scale(2.4);
      opacity: 0;
    }
  }
  
  /* 弹跳动画 */
  .bounce-in {
    animation: bounce-in 0.6s ease-out;
  }
  
  @keyframes bounce-in {
    0% {
      transform: scale(0.3);
      opacity: 0;
    }
    50% {
      transform: scale(1.05);
    }
    70% {
      transform: scale(0.9);
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }
  
  /* 滑入动画 */
  .slide-in-left {
    animation: slide-in-left 0.5s ease-out;
  }
  
  @keyframes slide-in-left {
    0% {
      transform: translateX(-100%);
      opacity: 0;
    }
    100% {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  .slide-in-right {
    animation: slide-in-right 0.5s ease-out;
  }
  
  @keyframes slide-in-right {
    0% {
      transform: translateX(100%);
      opacity: 0;
    }
    100% {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  /* 数字动画 */
  .number-flip {
    animation: number-flip 0.6s ease-in-out;
  }
  
  @keyframes number-flip {
    0% {
      transform: rotateY(0deg);
    }
    50% {
      transform: rotateY(90deg);
    }
    100% {
      transform: rotateY(0deg);
    }
  }
  
  /* 星星闪烁 */
  .star-twinkle {
    animation: star-twinkle 2s ease-in-out infinite;
  }
  
  @keyframes star-twinkle {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.1);
    }
  }
  
  /* 摇摆动画 */
  .wiggle-animation {
    animation: wiggle 0.8s ease-in-out;
  }
  
  @keyframes wiggle {
    0%, 7% {
      transform: rotateZ(0);
    }
    15% {
      transform: rotateZ(-15deg);
    }
    20% {
      transform: rotateZ(10deg);
    }
    25% {
      transform: rotateZ(-10deg);
    }
    30% {
      transform: rotateZ(6deg);
    }
    35% {
      transform: rotateZ(-4deg);
    }
    40%, 100% {
      transform: rotateZ(0);
    }
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --background-gradient: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .btn {
    @apply px-4 py-2 text-sm;
  }
  
  .card {
    @apply mx-2;
  }
  
  .math-expression {
    @apply text-lg p-3;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  * {
    background: white !important;
    color: black !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .card {
    @apply border-2 border-gray-800;
  }
  
  .btn {
    @apply border-2 border-current;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}