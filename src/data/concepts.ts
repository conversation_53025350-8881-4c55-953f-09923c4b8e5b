/**
 * 数学概念和教育理念数据
 * 基于胡小群老师的教育思想整理
 */

// 核心数学概念定义
export interface MathConcept {
  id: string;
  name: string;
  description: string;
  importance: string;
  examples: string[];
  misconceptions: string[];
  teachingTips: string[];
  relatedConcepts: string[];
}

// 教育理念和原则
export interface EducationPrinciple {
  id: string;
  title: string;
  description: string;
  rationale: string;
  applications: string[];
  examples: string[];
}

// 核心数学概念
export const mathConcepts: MathConcept[] = [
  {
    id: 'correspondence',
    name: '对应思想',
    description: '建立两个集合之间一一对应关系的数学思想，是数学学习的根本思想之一。',
    importance: '对应思想是计数、比较、运算等所有数学概念的基础，没有对应思想就没有数学知识。',
    examples: [
      '牧羊人用石头记录羊的数量',
      '小朋友找椅子的一一对应',
      '连线比较两组物品的多少',
      '函数中自变量与因变量的对应关系'
    ],
    misconceptions: [
      '认为对应只是简单的配对游戏',
      '忽视对应关系在数学中的根本地位',
      '只关注结果而不理解对应的过程'
    ],
    teachingTips: [
      '从具体的生活情境引入对应概念',
      '通过动手操作建立对应关系',
      '强调一个对一个的严格对应',
      '逐步从具体对应过渡到抽象对应'
    ],
    relatedConcepts: ['counting', 'comparison', 'function', 'mapping']
  },
  {
    id: 'counting',
    name: '计数概念',
    description: '通过对应关系确定集合中元素个数的过程，是数概念形成的基础。',
    importance: '计数是人类最基本的数学需求，是所有数学运算的起点。',
    examples: [
      '数羊群的数量',
      '数学用品的个数',
      '数手指头',
      '数数游戏'
    ],
    misconceptions: [
      '认为计数就是背诵数字顺序',
      '忽视计数与对应的内在联系',
      '只会机械计数而不理解数的意义'
    ],
    teachingTips: [
      '从一一对应开始教学计数',
      '结合实物进行计数练习',
      '强调计数的顺序性和唯一性',
      '培养计数策略和方法'
    ],
    relatedConcepts: ['correspondence', 'number_concept', 'cardinality']
  },
  {
    id: 'decimal_system',
    name: '十进制',
    description: '以10为基数的记数系统，体现了满十进一的位值概念。',
    importance: '十进制是我们日常使用的记数系统，理解其原理有助于掌握数的结构。',
    examples: [
      '十个一变成一个十',
      '鸡蛋十个一盒的包装',
      '计数器的珠子移动',
      '钱币的换算关系'
    ],
    misconceptions: [
      '认为十进制是自然的唯一选择',
      '不理解位值的真正含义',
      '混淆数字和数的概念'
    ],
    teachingTips: [
      '从具体的分组活动开始',
      '使用直观教具演示进位过程',
      '强调位置的重要性',
      '比较不同进制系统加深理解'
    ],
    relatedConcepts: ['place_value', 'grouping', 'base_ten', 'regrouping']
  },
  {
    id: 'place_value',
    name: '位值概念',
    description: '数字在不同位置上表示不同大小数值的概念。',
    importance: '位值概念是理解大数、运算算理和数学思维的关键。',
    examples: [
      '同样是2，在个位表示2，在十位表示20',
      '123中的1表示100，2表示20，3表示3',
      '零的占位作用',
      '小数点的位值意义'
    ],
    misconceptions: [
      '认为数字就是数，忽视位置的作用',
      '不理解零的占位意义',
      '运算时忽视位值对齐'
    ],
    teachingTips: [
      '使用计数器等教具直观展示',
      '强调同一数字在不同位置的不同含义',
      '通过分拆和组合加深理解',
      '结合实际情境讲解位值'
    ],
    relatedConcepts: ['decimal_system', 'number_sense', 'regrouping']
  },
  {
    id: 'number_comparison',
    name: '数的比较',
    description: '确定两个或多个数之间大小关系的过程。',
    importance: '比较是数概念的重要组成部分，培养数感和逻辑思维。',
    examples: [
      '比较两组物品的多少',
      '比较两个数的大小',
      '排列数的大小顺序',
      '估算和比较的结合'
    ],
    misconceptions: [
      '只看数字个数不看位值',
      '混淆数字大小和数的大小',
      '忽视比较的逻辑性'
    ],
    teachingTips: [
      '从直观比较开始',
      '教授系统的比较方法',
      '结合数轴进行比较',
      '培养估算能力'
    ],
    relatedConcepts: ['correspondence', 'place_value', 'ordering']
  },
  {
    id: 'addition_meaning',
    name: '加法意义',
    description: '加法的本质是合并或增加，可以从多个角度理解。',
    importance: '理解加法意义是掌握加法运算和培养数感的基础。',
    examples: [
      '两堆物品合并在一起',
      '在数轴上向右移动',
      '建立对应关系',
      '重复计数过程'
    ],
    misconceptions: [
      '认为加法只是机械的运算过程',
      '不理解加法的多种意义',
      '忽视加法与实际情境的联系'
    ],
    teachingTips: [
      '从多个角度理解加法',
      '结合具体情境讲解',
      '使用多种表征方式',
      '强调加法的可逆性'
    ],
    relatedConcepts: ['subtraction_meaning', 'number_sense', 'operations']
  },
  {
    id: 'subtraction_meaning',
    name: '减法意义',
    description: '减法是加法的逆运算，表示拿走、比较或求差。',
    importance: '理解减法意义有助于掌握减法运算和加减法关系。',
    examples: [
      '从一堆物品中拿走一部分',
      '比较两个数的差距',
      '在数轴上向左移动',
      '求未知的加数'
    ],
    misconceptions: [
      '认为减法和加法完全独立',
      '不理解减法的多种含义',
      '害怕减法运算'
    ],
    teachingTips: [
      '从具体操作开始',
      '强调加减法的互逆关系',
      '使用多种情境理解减法',
      '培养灵活的思维方式'
    ],
    relatedConcepts: ['addition_meaning', 'inverse_operations', 'number_sense']
  }
];

// 教育理念和原则
export const educationPrinciples: EducationPrinciple[] = [
  {
    id: 'thinking_over_knowledge',
    title: '数学思想重于数学知识',
    description: '数学思想是数学知识的根本，比具体的计算技能更重要。',
    rationale: '数学思想是数学知识的"父母"，没有数学思想就不会有数学知识。思想比知识更持久、更重要。',
    applications: [
      '重视概念理解而非机械练习',
      '培养数学思维而非计算速度',
      '强调"为什么"而非"怎么做"',
      '从思想高度统领具体知识'
    ],
    examples: [
      '对应思想统领计数、比较、运算',
      '数形结合思想贯穿数学学习',
      '化归思想指导问题解决',
      '抽象思想引导概念建构'
    ]
  },
  {
    id: 'concrete_to_abstract',
    title: '从具体到抽象',
    description: '遵循儿童认知规律，从具体操作逐步过渡到抽象思维。',
    rationale: '儿童的思维发展遵循具体→半抽象→抽象的规律，教学应该顺应这一规律。',
    applications: [
      '使用具体教具和实物',
      '提供丰富的操作体验',
      '逐步减少具体支撑',
      '最终达到抽象理解'
    ],
    examples: [
      '从石头计数到抽象数字',
      '从实物操作到符号运算',
      '从图形表示到代数表达',
      '从具体情境到数学模型'
    ]
  },
  {
    id: 'multiple_representations',
    title: '多元表征理解',
    description: '使用多种方式表达和理解同一数学概念。',
    rationale: '多元表征有助于学生从不同角度理解概念，建立完整的认知结构。',
    applications: [
      '数、形、式多种表征结合',
      '语言、图像、符号并用',
      '静态、动态表征配合',
      '具体、抽象表征转换'
    ],
    examples: [
      '用数数、图形、对应理解加法',
      '用横线、圆圈、数字表示数量',
      '用故事、图片、算式表达问题',
      '用实物、模型、符号学习概念'
    ]
  },
  {
    id: 'understanding_first',
    title: '理解先于技能',
    description: '重视概念理解，在理解基础上发展技能。',
    rationale: '理解是技能的基础，有了理解才能灵活运用技能，才能实现真正的学习。',
    applications: [
      '先理解算理再练习算法',
      '先掌握概念再应用公式',
      '先建立模型再解决问题',
      '先理解原理再记忆规则'
    ],
    examples: [
      '理解十进制原理后学习进位',
      '理解加法意义后练习计算',
      '理解竖式原理后掌握算法',
      '理解比较方法后应用技巧'
    ]
  },
  {
    id: 'error_as_resource',
    title: '错误是学习资源',
    description: '将学生的错误看作宝贵的学习资源，从中发现思维规律。',
    rationale: '错误反映了学生的真实思维状态，是调整教学和深化理解的重要信息。',
    applications: [
      '分析错误背后的思维过程',
      '利用错误进行对比教学',
      '引导学生自我反思和纠错',
      '将错误转化为学习机会'
    ],
    examples: [
      '分析计算错误的原因',
      '从错误中发现概念误区',
      '利用错误讲解正确方法',
      '鼓励学生从错误中学习'
    ]
  },
  {
    id: 'student_centered',
    title: '以学生为中心',
    description: '根据学生的认知特点和学习需求设计教学。',
    rationale: '教学应该适应学生而不是让学生适应教学，要尊重学生的个体差异。',
    applications: [
      '了解学生的认知起点',
      '关注学生的思维过程',
      '提供个性化的学习支持',
      '培养学生的自主学习能力'
    ],
    examples: [
      '根据学生基础调整教学难度',
      '提供多种解题策略选择',
      '关注不同学生的学习节奏',
      '鼓励学生表达自己的想法'
    ]
  }
];

/**
 * 获取数学概念
 */
export const getMathConcepts = (): MathConcept[] => mathConcepts;

/**
 * 根据ID获取数学概念
 */
export const getConceptById = (id: string): MathConcept | undefined => {
  return mathConcepts.find(concept => concept.id === id);
};

/**
 * 获取教育原则
 */
export const getEducationPrinciples = (): EducationPrinciple[] => educationPrinciples;

/**
 * 根据ID获取教育原则
 */
export const getPrincipleById = (id: string): EducationPrinciple | undefined => {
  return educationPrinciples.find(principle => principle.id === id);
};

/**
 * 获取相关概念
 */
export const getRelatedConcepts = (conceptId: string): MathConcept[] => {
  const concept = getConceptById(conceptId);
  if (!concept) return [];
  
  return concept.relatedConcepts
    .map(id => getConceptById(id))
    .filter(Boolean) as MathConcept[];
};

/**
 * 搜索概念
 */
export const searchConcepts = (keyword: string): MathConcept[] => {
  const lowercaseKeyword = keyword.toLowerCase();
  return mathConcepts.filter(concept => 
    concept.name.toLowerCase().includes(lowercaseKeyword) ||
    concept.description.toLowerCase().includes(lowercaseKeyword) ||
    concept.examples.some(example => example.toLowerCase().includes(lowercaseKeyword))
  );
};