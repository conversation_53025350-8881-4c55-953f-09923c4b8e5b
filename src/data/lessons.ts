import { Lesson } from '@/types';

/**
 * 胡小群数学教程课程数据
 * 基于原文档内容进行结构化整理
 */
export const lessons: Lesson[] = [
  {
    id: 'lesson-01',
    title: '从对应到计数',
    subtitle: '建立对应思想，理解计数本质',
    description: '通过古代牧羊人计数的故事，学习对应思想。理解为什么数学思想比数学知识更重要，掌握用对应方法进行计数和比较大小。',
    duration: 25,
    difficulty: 1,
    prerequisites: [],
    concepts: ['对应思想', '计数概念', '一一对应', '比较大小', '数字命名'],
    status: 'available',
    activities: [
      {
        id: 'act-01-01',
        type: 'animation',
        title: '古代牧羊人的计数智慧',
        description: '观看牧羊人用石头记录羊群数量的动画故事',
        completed: false,
        attempts: 0,
        config: {
          type: 'animation',
          duration: 180,
          autoPlay: true,
          controls: true,
          scenes: [
            {
              id: 'scene-01',
              duration: 60,
              narration: '很久很久以前，有一个牧羊人，他每天带着羊群出去放牧...'
            },
            {
              id: 'scene-02', 
              duration: 60,
              narration: '出门时，每出去一只羊，他就往口袋里放一颗石头...'
            },
            {
              id: 'scene-03',
              duration: 60,
              narration: '回家时，每进来一只羊，他就扔掉一颗石头...'
            }
          ]
        }
      },
      {
        id: 'act-01-02',
        type: 'interactive',
        title: '对应连线游戏',
        description: '用连线的方法比较圆圈和三角形的多少',
        completed: false,
        attempts: 0,
        config: {
          type: 'interactive',
          tool: 'comparison',
          instructions: '请用连线的方法比较圆圈和三角形谁更多',
          hints: [
            '每个圆圈连接一个三角形',
            '看看最后哪种图形剩下更多',
            '剩下的图形就是数量更多的'
          ],
          validation: [
            {
              field: 'connections',
              rule: 'required',
              message: '请完成所有的连线'
            }
          ]
        }
      },
      {
        id: 'act-01-03',
        type: 'game',
        title: '小朋友找椅子',
        description: '帮助小朋友找到合适的椅子，理解对应关系',
        completed: false,
        attempts: 0,
        config: {
          type: 'game',
          gameType: 'matching',
          rules: '每个小朋友都要找到一个椅子坐下',
          timeLimit: 120,
          levels: [
            {
              id: 'level-1',
              difficulty: 1,
              objectives: ['所有小朋友都有椅子坐'],
              successCriteria: {
                accuracy: 100
              }
            }
          ]
        }
      }
    ]
  },
  {
    id: 'lesson-02', 
    title: '对应与十进制',
    subtitle: '理解十进制的产生和位值概念',
    description: '从古代计数方法演进到现代十进制系统，理解为什么满十进一，掌握个位、十位的概念和数位对齐的重要性。',
    duration: 30,
    difficulty: 2,
    prerequisites: ['lesson-01'],
    concepts: ['十进制', '满十进一', '位值概念', '个位十位', '数位对齐'],
    status: 'locked',
    activities: [
      {
        id: 'act-02-01',
        type: 'animation',
        title: '从横线到圆圈的进化',
        description: '观看横线变圆圈，理解进位的本质',
        completed: false,
        attempts: 0,
        config: {
          type: 'animation',
          duration: 240,
          autoPlay: true,
          controls: true,
          scenes: [
            {
              id: 'scene-01',
              duration: 80,
              narration: '古人用横线记录数量，但横线太多就很难数清楚...'
            },
            {
              id: 'scene-02',
              duration: 80, 
              narration: '于是他们想到了好办法：十条横线用一个圆圈来表示！'
            },
            {
              id: 'scene-03',
              duration: 80,
              narration: '这就是满十进一的由来，也是十进制的基础...'
            }
          ]
        }
      },
      {
        id: 'act-02-02',
        type: 'interactive',
        title: '鸡蛋装盒游戏',
        description: '把鸡蛋按十个一盒的方式整理，理解十进制',
        completed: false,
        attempts: 0,
        config: {
          type: 'interactive',
          tool: 'visualization',
          instructions: '将散装的鸡蛋按照十个一盒的方式整理好',
          hints: [
            '每盒只能装10个鸡蛋',
            '装满一盒后再装下一盒',
            '剩余不足10个的单独放置'
          ],
          validation: [
            {
              field: 'boxes',
              rule: 'exact',
              value: 10,
              message: '每盒必须装满10个鸡蛋'
            }
          ]
        }
      }
    ]
  },
  {
    id: 'lesson-03',
    title: '数的大小和比较', 
    subtitle: '掌握数的比较方法和大小关系',
    description: '学习比较两个数大小的方法：先看位数，再看数位。理解大于号、小于号、等于号的含义和使用。',
    duration: 25,
    difficulty: 2,
    prerequisites: ['lesson-02'],
    concepts: ['数的比较', '位数比较', '数位比较', '大于小于等于', '组数问题'],
    status: 'locked',
    activities: [
      {
        id: 'act-03-01',
        type: 'interactive',
        title: '数字比大小',
        description: '通过直观的方法比较两个数的大小',
        completed: false,
        attempts: 0,
        config: {
          type: 'interactive',
          tool: 'comparison',
          instructions: '比较两个数的大小，选择正确的符号',
          hints: [
            '先看谁的位数多',
            '位数一样时看最高位',
            '最高位一样时看下一位'
          ],
          validation: [
            {
              field: 'comparison',
              rule: 'required',
              message: '请选择正确的比较符号'
            }
          ]
        }
      },
      {
        id: 'act-03-02',
        type: 'game',
        title: '组数大挑战',
        description: '用给定的数字卡片组成最大或最小的数',
        completed: false,
        attempts: 0,
        config: {
          type: 'game',
          gameType: 'puzzle',
          rules: '用给定的数字卡片组成要求的数',
          levels: [
            {
              id: 'level-1',
              difficulty: 1,
              objectives: ['组成最大的两位数', '组成最小的两位数'],
              successCriteria: {
                accuracy: 100
              }
            }
          ]
        }
      }
    ]
  },
  {
    id: 'lesson-04',
    title: '加减法的奥秘',
    subtitle: '深入理解加减法的本质和意义',
    description: '从三种角度理解加法：数数、图形、对应。学习加法交换律，理解减法与加法的互逆关系。',
    duration: 35,
    difficulty: 3,
    prerequisites: ['lesson-03'],
    concepts: ['加法意义', '减法意义', '加法交换律', '数形结合', '互逆运算'],
    status: 'locked',
    activities: [
      {
        id: 'act-04-01',
        type: 'animation',
        title: '加法的三种理解',
        description: '从数数、图形、对应三个角度理解加法',
        completed: false,
        attempts: 0,
        config: {
          type: 'animation',
          duration: 300,
          autoPlay: true,
          controls: true,
          scenes: [
            {
              id: 'scene-01',
              duration: 100,
              narration: '加法可以从数数的角度理解：接着数下去...'
            },
            {
              id: 'scene-02',
              duration: 100,
              narration: '也可以从图形的角度理解：在数轴上向右移动...'
            },
            {
              id: 'scene-03',
              duration: 100,
              narration: '还可以从对应的角度理解：建立一一对应关系...'
            }
          ]
        }
      },
      {
        id: 'act-04-02',
        type: 'interactive',
        title: '数轴上的加减法',
        description: '在数轴上操作，直观理解加减法',
        completed: false,
        attempts: 0,
        config: {
          type: 'interactive',
          tool: 'visualization',
          instructions: '在数轴上完成加减法运算',
          hints: [
            '加法是向右移动',
            '减法是向左移动',
            '观察起点和终点的数字'
          ],
          validation: [
            {
              field: 'result',
              rule: 'numeric',
              message: '请给出正确的计算结果'
            }
          ]
        }
      }
    ]
  },
  {
    id: 'lesson-05',
    title: '凑十、平十和破十',
    subtitle: '掌握20以内加减法的计算策略',
    description: '学习三种重要的计算方法：凑十法、平十法、破十法。理解这些方法背后的思维逻辑和应用场景。',
    duration: 40,
    difficulty: 3,
    prerequisites: ['lesson-04'],
    concepts: ['凑十法', '平十法', '破十法', '好朋友数', '计算策略'],
    status: 'locked',
    activities: [
      {
        id: 'act-05-01',
        type: 'animation',
        title: '鸡蛋盒的奥秘',
        description: '通过鸡蛋装盒理解凑十、平十、破十的原理',
        completed: false,
        attempts: 0,
        config: {
          type: 'animation',
          duration: 360,
          autoPlay: true,
          controls: true,
          scenes: [
            {
              id: 'scene-01',
              duration: 120,
              narration: '凑十法：把两盒没装满的鸡蛋重新整理...'
            },
            {
              id: 'scene-02',
              duration: 120,
              narration: '平十法：先拿完散装的，再拿整盒的...'
            },
            {
              id: 'scene-03',
              duration: 120,
              narration: '破十法：把一盒鸡蛋拆开来用...'
            }
          ]
        }
      },
      {
        id: 'act-05-02',
        type: 'game',
        title: '好朋友找伙伴',
        description: '找出能凑成10的数字好朋友',
        completed: false,
        attempts: 0,
        config: {
          type: 'game',
          gameType: 'matching',
          rules: '找出所有能凑成10的数字对',
          levels: [
            {
              id: 'level-1',
              difficulty: 1,
              objectives: ['找出所有好朋友数对'],
              successCriteria: {
                accuracy: 100,
                time: 60
              }
            }
          ]
        }
      }
    ]
  },
  {
    id: 'lesson-06',
    title: '加减竖式运算',
    subtitle: '学习规范的竖式计算方法',
    description: '理解竖式运算的三个原则：数位对齐、从低位到高位、满十进一/借一当十。掌握进位和退位的计算方法。',
    duration: 35,
    difficulty: 4,
    prerequisites: ['lesson-05'],
    concepts: ['竖式运算', '数位对齐', '进位退位', '计算规范', '算理理解'],
    status: 'locked',
    activities: [
      {
        id: 'act-06-01',
        type: 'animation',
        title: '竖式的由来',
        description: '从古代木片计数到现代竖式的演变过程',
        completed: false,
        attempts: 0,
        config: {
          type: 'animation',
          duration: 240,
          autoPlay: true,
          controls: true,
          scenes: [
            {
              id: 'scene-01',
              duration: 80,
              narration: '古代人用两块木片分别记录个位和十位...'
            },
            {
              id: 'scene-02',
              duration: 80,
              narration: '为了避免木片位置搞错，人们发明了竖式...'
            },
            {
              id: 'scene-03',
              duration: 80,
              narration: '竖式的关键是数位对齐，个位对个位，十位对十位...'
            }
          ]
        }
      },
      {
        id: 'act-06-02',
        type: 'interactive',
        title: '竖式计算练习',
        description: '分步骤完成竖式加减法计算',
        completed: false,
        attempts: 0,
        config: {
          type: 'interactive',
          tool: 'calculation',
          instructions: '按照竖式运算的步骤完成计算',
          hints: [
            '先对齐数位',
            '从个位开始计算',
            '注意进位和退位'
          ],
          validation: [
            {
              field: 'alignment',
              rule: 'required',
              message: '请确保数位对齐'
            },
            {
              field: 'result',
              rule: 'numeric',
              message: '请给出正确的计算结果'
            }
          ]
        }
      }
    ]
  },
  {
    id: 'lesson-07',
    title: '数字谜基础（一）',
    subtitle: '初步接触数字推理问题',
    description: '学习简单的数字谜题，培养逻辑推理能力。理解竖式数字谜的解题思路和填空题的解决方法。',
    duration: 30,
    difficulty: 4,
    prerequisites: ['lesson-06'],
    concepts: ['数字谜', '逻辑推理', '竖式推理', '符号填空', '问题解决'],
    status: 'locked',
    activities: [
      {
        id: 'act-07-01',
        type: 'interactive',
        title: '竖式填空题',
        description: '在竖式中填入缺失的数字',
        completed: false,
        attempts: 0,
        config: {
          type: 'interactive',
          tool: 'puzzle',
          instructions: '根据竖式运算规则填入正确的数字',
          hints: [
            '考虑是否有进位或退位',
            '从简单的位置开始推理',
            '利用已知条件逐步推导'
          ],
          validation: [
            {
              field: 'solution',
              rule: 'exact',
              message: '请填入正确的数字'
            }
          ]
        }
      }
    ]
  },
  {
    id: 'lesson-08',
    title: '加减巧算（一）',
    subtitle: '学习提高计算速度的技巧',
    description: '学习加减法的巧算方法：凑整、调顺序、分组计算。理解为什么能这样算的算理。',
    duration: 35,
    difficulty: 4,
    prerequisites: ['lesson-07'],
    concepts: ['巧算方法', '凑整计算', '交换律', '结合律', '算理理解'],
    status: 'locked',
    activities: [
      {
        id: 'act-08-01',
        type: 'game',
        title: '巧算闯关',
        description: '用巧算方法快速完成计算挑战',
        completed: false,
        attempts: 0,
        config: {
          type: 'game',
          gameType: 'challenge',
          rules: '在限定时间内用巧算方法完成计算',
          timeLimit: 180,
          levels: [
            {
              id: 'level-1',
              difficulty: 1,
              objectives: ['用凑整法计算加法'],
              successCriteria: {
                accuracy: 80,
                time: 60
              }
            }
          ]
        }
      }
    ]
  },
  {
    id: 'lesson-09',
    title: '数字谜基础（二）',
    subtitle: '进阶数字推理和比较问题',
    description: '学习更复杂的数字谜题，包括符号填空、大小比较推理等。培养系统性的逻辑思维能力。',
    duration: 35,
    difficulty: 5,
    prerequisites: ['lesson-08'],
    concepts: ['复杂推理', '比较推理', '系统思维', '多步推理', '综合应用'],
    status: 'locked',
    activities: [
      {
        id: 'act-09-01',
        type: 'game',
        title: '数字侦探',
        description: '解决复杂的数字谜题，成为数字侦探',
        completed: false,
        attempts: 0,
        config: {
          type: 'game',
          gameType: 'puzzle',
          rules: '运用逻辑推理解决数字谜题',
          levels: [
            {
              id: 'level-1',
              difficulty: 3,
              objectives: ['解决符号填空问题', '完成大小比较推理'],
              successCriteria: {
                accuracy: 90
              }
            }
          ]
        }
      }
    ]
  }
];

/**
 * 获取课程列表
 */
export const getLessons = (): Lesson[] => lessons;

/**
 * 根据ID获取课程
 */
export const getLessonById = (id: string): Lesson | undefined => {
  return lessons.find(lesson => lesson.id === id);
};

/**
 * 获取可用的课程（已解锁的）
 */
export const getAvailableLessons = (): Lesson[] => {
  return lessons.filter(lesson => lesson.status === 'available' || lesson.status === 'in_progress' || lesson.status === 'completed');
};

/**
 * 获取课程的前置条件
 */
export const getPrerequisites = (lessonId: string): Lesson[] => {
  const lesson = getLessonById(lessonId);
  if (!lesson) return [];
  
  return lesson.prerequisites.map(prereqId => getLessonById(prereqId)).filter(Boolean) as Lesson[];
};

/**
 * 检查课程是否可以解锁
 */
export const canUnlockLesson = (lessonId: string, completedLessons: string[]): boolean => {
  const lesson = getLessonById(lessonId);
  if (!lesson) return false;
  
  // 检查所有前置课程是否已完成
  return lesson.prerequisites.every(prereqId => completedLessons.includes(prereqId));
};

/**
 * 获取课程难度描述
 */
export const getDifficultyLabel = (difficulty: number): string => {
  const labels = {
    1: '入门',
    2: '基础', 
    3: '进阶',
    4: '挑战',
    5: '精通'
  };
  return labels[difficulty as keyof typeof labels] || '未知';
};

/**
 * 计算课程总时长
 */
export const getTotalDuration = (): number => {
  return lessons.reduce((total, lesson) => total + lesson.duration, 0);
};

/**
 * 获取涉及特定概念的课程
 */
export const getLessonsByConcept = (concept: string): Lesson[] => {
  return lessons.filter(lesson => lesson.concepts.includes(concept));
};