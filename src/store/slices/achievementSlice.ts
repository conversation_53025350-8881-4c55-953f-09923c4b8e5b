import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// 成就类型
export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  category: 'learning' | 'progress' | 'streak' | 'score' | 'special';
  condition: {
    type: 'lesson_complete' | 'score_threshold' | 'streak_days' | 'total_time' | 'perfect_score';
    value: number;
    target?: string; // 特定课程ID等
  };
  reward: {
    points: number;
    badge?: string;
    title?: string;
  };
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  unlocked: boolean;
  unlockedAt?: string;
}

// 学生成就状态
export interface StudentAchievement {
  studentId: string;
  achievementId: string;
  unlockedAt: string;
  progress?: number; // 0-100，用于显示进度
}

// 积分记录
export interface PointsRecord {
  id: string;
  studentId: string;
  points: number;
  source: 'achievement' | 'lesson_complete' | 'perfect_score' | 'daily_bonus';
  sourceId: string;
  description: string;
  createdAt: string;
}

// 状态类型
interface AchievementState {
  achievements: Achievement[];
  studentAchievements: StudentAchievement[];
  pointsRecords: PointsRecord[];
  loading: boolean;
  error: string | null;
}

// 初始状态
const initialState: AchievementState = {
  achievements: [
    {
      id: 'first_lesson',
      title: '初学者',
      description: '完成第一个课程',
      icon: '🎓',
      category: 'learning',
      condition: { type: 'lesson_complete', value: 1 },
      reward: { points: 100, badge: 'beginner' },
      rarity: 'common',
      unlocked: false,
    },
    {
      id: 'perfect_score',
      title: '完美主义者',
      description: '在任意活动中获得满分',
      icon: '⭐',
      category: 'score',
      condition: { type: 'perfect_score', value: 100 },
      reward: { points: 200, badge: 'perfectionist' },
      rarity: 'rare',
      unlocked: false,
    },
    {
      id: 'week_streak',
      title: '坚持不懈',
      description: '连续学习7天',
      icon: '🔥',
      category: 'streak',
      condition: { type: 'streak_days', value: 7 },
      reward: { points: 500, badge: 'persistent' },
      rarity: 'epic',
      unlocked: false,
    },
  ],
  studentAchievements: [],
  pointsRecords: [],
  loading: false,
  error: null,
};

// 创建slice
const achievementSlice = createSlice({
  name: 'achievement',
  initialState,
  reducers: {
    // 解锁成就
    unlockAchievement: (state, action: PayloadAction<{ studentId: string; achievementId: string }>) => {
      const { studentId, achievementId } = action.payload;
      const achievement = state.achievements.find(a => a.id === achievementId);
      
      if (achievement && !achievement.unlocked) {
        // 标记成就为已解锁
        achievement.unlocked = true;
        achievement.unlockedAt = new Date().toISOString();
        
        // 添加学生成就记录
        const studentAchievement: StudentAchievement = {
          studentId,
          achievementId,
          unlockedAt: new Date().toISOString(),
        };
        state.studentAchievements.push(studentAchievement);
        
        // 添加积分记录
        const pointsRecord: PointsRecord = {
          id: `points-${Date.now()}`,
          studentId,
          points: achievement.reward.points,
          source: 'achievement',
          sourceId: achievementId,
          description: `解锁成就：${achievement.title}`,
          createdAt: new Date().toISOString(),
        };
        state.pointsRecords.push(pointsRecord);
      }
    },

    // 添加积分记录
    addPointsRecord: (state, action: PayloadAction<Omit<PointsRecord, 'id' | 'createdAt'>>) => {
      const pointsRecord: PointsRecord = {
        ...action.payload,
        id: `points-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        createdAt: new Date().toISOString(),
      };
      state.pointsRecords.push(pointsRecord);
    },

    // 检查成就条件
    checkAchievements: (state, action: PayloadAction<{
      studentId: string;
      type: 'lesson_complete' | 'score_achieved' | 'streak_updated' | 'time_spent';
      data: any;
    }>) => {
      const { studentId, type, data } = action.payload;
      
      state.achievements.forEach(achievement => {
        if (achievement.unlocked) return;
        
        const isAlreadyUnlocked = state.studentAchievements.some(
          sa => sa.studentId === studentId && sa.achievementId === achievement.id
        );
        
        if (isAlreadyUnlocked) return;
        
        let shouldUnlock = false;
        
        switch (achievement.condition.type) {
          case 'lesson_complete':
            if (type === 'lesson_complete' && data.completedLessons >= achievement.condition.value) {
              shouldUnlock = true;
            }
            break;
          case 'perfect_score':
            if (type === 'score_achieved' && data.score >= achievement.condition.value) {
              shouldUnlock = true;
            }
            break;
          case 'streak_days':
            if (type === 'streak_updated' && data.streakDays >= achievement.condition.value) {
              shouldUnlock = true;
            }
            break;
          case 'total_time':
            if (type === 'time_spent' && data.totalTime >= achievement.condition.value) {
              shouldUnlock = true;
            }
            break;
        }
        
        if (shouldUnlock) {
          // 解锁成就
          achievement.unlocked = true;
          achievement.unlockedAt = new Date().toISOString();
          
          // 添加学生成就记录
          const studentAchievement: StudentAchievement = {
            studentId,
            achievementId: achievement.id,
            unlockedAt: new Date().toISOString(),
          };
          state.studentAchievements.push(studentAchievement);
          
          // 添加积分记录
          const pointsRecord: PointsRecord = {
            id: `points-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            studentId,
            points: achievement.reward.points,
            source: 'achievement',
            sourceId: achievement.id,
            description: `解锁成就：${achievement.title}`,
            createdAt: new Date().toISOString(),
          };
          state.pointsRecords.push(pointsRecord);
        }
      });
    },

    // 重置学生成就
    resetStudentAchievements: (state, action: PayloadAction<{ studentId: string }>) => {
      const { studentId } = action.payload;
      state.studentAchievements = state.studentAchievements.filter(
        sa => sa.studentId !== studentId
      );
      state.pointsRecords = state.pointsRecords.filter(
        pr => pr.studentId !== studentId
      );
    },

    // 设置加载状态
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },

    // 设置错误
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },

    // 清除错误
    clearError: (state) => {
      state.error = null;
    },
  },
});

// 导出actions
export const {
  unlockAchievement,
  addPointsRecord,
  checkAchievements,
  resetStudentAchievements,
  setLoading,
  setError,
  clearError,
} = achievementSlice.actions;

// 选择器
export const selectAchievements = (state: { achievement: AchievementState }) => state.achievement.achievements;
export const selectStudentAchievements = (state: { achievement: AchievementState }) => state.achievement.studentAchievements;
export const selectPointsRecords = (state: { achievement: AchievementState }) => state.achievement.pointsRecords;
export const selectAchievementLoading = (state: { achievement: AchievementState }) => state.achievement.loading;
export const selectAchievementError = (state: { achievement: AchievementState }) => state.achievement.error;

// 复合选择器
export const selectStudentSpecificAchievements = (studentId: string) => 
  (state: { achievement: AchievementState }) =>
    state.achievement.studentAchievements.filter(sa => sa.studentId === studentId);

export const selectStudentTotalPoints = (studentId: string) => 
  (state: { achievement: AchievementState }) =>
    state.achievement.pointsRecords
      .filter(pr => pr.studentId === studentId)
      .reduce((total, pr) => total + pr.points, 0);

// 导出reducer
export default achievementSlice.reducer;
