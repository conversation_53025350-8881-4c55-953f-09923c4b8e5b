import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface CorrespondenceGameProps {
  onComplete: (score: number) => void;
  onNext?: () => void;
}

interface Item {
  id: number;
  x: number;
  y: number;
  isMatched: boolean;
}

interface Connection {
  fromId: number;
  toId: number;
  isCorrect: boolean;
}

const CorrespondenceGame: React.FC<CorrespondenceGameProps> = ({ onComplete, onNext }) => {
  const [leftItems, setLeftItems] = useState<Item[]>([]);
  const [rightItems, setRightItems] = useState<Item[]>([]);
  const [connections, setConnections] = useState<Connection[]>([]);
  const [dragStart, setDragStart] = useState<{ id: number; x: number; y: number } | null>(null);
  const [dragEnd, setDragEnd] = useState<{ x: number; y: number } | null>(null);
  const [score, setScore] = useState(0);
  const [attempts, setAttempts] = useState(0);
  const [isComplete, setIsComplete] = useState(false);
  const [feedback, setFeedback] = useState('');
  const svgRef = useRef<SVGSVGElement>(null);

  // 初始化游戏
  useEffect(() => {
    const leftCount = 5;
    const rightCount = 5;
    
    const newLeftItems: Item[] = Array.from({ length: leftCount }, (_, i) => ({
      id: i,
      x: 100,
      y: 80 + i * 60,
      isMatched: false
    }));
    
    const newRightItems: Item[] = Array.from({ length: rightCount }, (_, i) => ({
      id: i + leftCount,
      x: 400,
      y: 80 + i * 60,
      isMatched: false
    }));
    
    setLeftItems(newLeftItems);
    setRightItems(newRightItems);
  }, []);

  // 检查游戏完成状态
  useEffect(() => {
    if (leftItems.length > 0 && rightItems.length > 0) {
      const allMatched = leftItems.every(item => item.isMatched) && 
                         rightItems.every(item => item.isMatched);
      
      if (allMatched && !isComplete) {
        setIsComplete(true);
        const finalScore = Math.max(0, 100 - (attempts - leftItems.length) * 10);
        setScore(finalScore);
        setFeedback('太棒了！你成功建立了所有的对应关系！');
        setTimeout(() => onComplete(finalScore), 1500);
      }
    }
  }, [leftItems, rightItems, attempts, isComplete, onComplete]);

  const handleMouseDown = (item: Item, side: 'left' | 'right') => {
    if (item.isMatched) return;
    
    setDragStart({ id: item.id, x: item.x, y: item.y });
    setDragEnd(null);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!dragStart || !svgRef.current) return;
    
    const rect = svgRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    setDragEnd({ x, y });
  };

  const handleMouseUp = (targetItem?: Item) => {
    if (!dragStart) return;
    
    if (targetItem && !targetItem.isMatched) {
      // 检查是否是正确的配对
      const sourceIsLeft = dragStart.id < 5;
      const targetIsLeft = targetItem.id < 5;
      
      if (sourceIsLeft !== targetIsLeft) {
        // 一对一对应关系检查
        const sourceIndex = sourceIsLeft ? dragStart.id : dragStart.id - 5;
        const targetIndex = targetIsLeft ? targetItem.id : targetItem.id - 5;
        
        const isCorrect = sourceIndex === targetIndex;
        
        setAttempts(prev => prev + 1);
        
        if (isCorrect) {
          // 正确匹配
          const newConnection: Connection = {
            fromId: dragStart.id,
            toId: targetItem.id,
            isCorrect: true
          };
          
          setConnections(prev => [...prev, newConnection]);
          
          // 标记为已匹配
          setLeftItems(prev => prev.map(item => 
            item.id === dragStart.id || item.id === targetItem.id 
              ? { ...item, isMatched: true } 
              : item
          ));
          setRightItems(prev => prev.map(item => 
            item.id === dragStart.id || item.id === targetItem.id 
              ? { ...item, isMatched: true } 
              : item
          ));
          
          setFeedback('正确！继续努力！');
        } else {
          // 错误匹配
          setFeedback('再试试看，找到正确的对应关系！');
        }
        
        setTimeout(() => setFeedback(''), 2000);
      }
    }
    
    setDragStart(null);
    setDragEnd(null);
  };

  const resetGame = () => {
    setLeftItems(prev => prev.map(item => ({ ...item, isMatched: false })));
    setRightItems(prev => prev.map(item => ({ ...item, isMatched: false })));
    setConnections([]);
    setScore(0);
    setAttempts(0);
    setIsComplete(false);
    setFeedback('');
    setDragStart(null);
    setDragEnd(null);
  };

  return (
    <div className="w-full h-96 bg-gradient-to-br from-purple-100 to-pink-100 rounded-lg relative overflow-hidden border-4 border-purple-300">
      {/* 游戏标题 */}
      <div className="absolute top-4 left-1/2 transform -translate-x-1/2 text-center">
        <h3 className="text-lg font-bold text-purple-800 mb-1">连线游戏</h3>
        <p className="text-sm text-purple-600">将左边的圆形与右边的三角形一一对应连接</p>
      </div>

      {/* SVG画布用于绘制连线 */}
      <svg
        ref={svgRef}
        className="absolute inset-0 w-full h-full pointer-events-none"
        onMouseMove={handleMouseMove}
        onMouseUp={() => handleMouseUp()}
      >
        {/* 绘制已建立的连接 */}
        {connections.map((connection, index) => {
          const fromItem = [...leftItems, ...rightItems].find(item => item.id === connection.fromId);
          const toItem = [...leftItems, ...rightItems].find(item => item.id === connection.toId);
          
          if (!fromItem || !toItem) return null;
          
          return (
            <motion.line
              key={index}
              x1={fromItem.x + 15}
              y1={fromItem.y + 15}
              x2={toItem.x + 15}
              y2={toItem.y + 15}
              stroke={connection.isCorrect ? '#10B981' : '#EF4444'}
              strokeWidth="3"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 0.5 }}
            />
          );
        })}
        
        {/* 绘制拖拽中的连线 */}
        {dragStart && dragEnd && (
          <line
            x1={dragStart.x + 15}
            y1={dragStart.y + 15}
            x2={dragEnd.x}
            y2={dragEnd.y}
            stroke="#6B7280"
            strokeWidth="2"
            strokeDasharray="5,5"
          />
        )}
      </svg>

      {/* 左侧圆形 */}
      <div className="absolute left-0 top-16">
        {leftItems.map((item) => (
          <motion.div
            key={item.id}
            className={`absolute w-8 h-8 rounded-full border-3 cursor-pointer transition-all ${
              item.isMatched 
                ? 'bg-green-400 border-green-600' 
                : 'bg-blue-400 border-blue-600 hover:bg-blue-500'
            }`}
            style={{ left: item.x, top: item.y }}
            onMouseDown={() => handleMouseDown(item, 'left')}
            onMouseUp={() => handleMouseUp(item)}
            whileHover={{ scale: item.isMatched ? 1 : 1.1 }}
            whileTap={{ scale: 0.95 }}
          >
            <div className="w-full h-full flex items-center justify-center text-white font-bold text-sm">
              {item.id + 1}
            </div>
          </motion.div>
        ))}
      </div>

      {/* 右侧三角形 */}
      <div className="absolute right-0 top-16">
        {rightItems.map((item) => (
          <motion.div
            key={item.id}
            className={`absolute w-8 h-8 cursor-pointer transition-all ${
              item.isMatched ? 'opacity-100' : 'opacity-80 hover:opacity-100'
            }`}
            style={{ left: item.x, top: item.y }}
            onMouseDown={() => handleMouseDown(item, 'right')}
            onMouseUp={() => handleMouseUp(item)}
            whileHover={{ scale: item.isMatched ? 1 : 1.1 }}
            whileTap={{ scale: 0.95 }}
          >
            <div 
              className={`w-0 h-0 border-l-4 border-r-4 border-b-8 border-transparent ${
                item.isMatched 
                  ? 'border-b-green-500' 
                  : 'border-b-red-500'
              }`}
              style={{ 
                borderLeftColor: 'transparent',
                borderRightColor: 'transparent',
                marginLeft: '50%',
                transform: 'translateX(-50%)'
              }}
            />
            <div className="absolute inset-0 flex items-center justify-center text-white font-bold text-xs">
              {String.fromCharCode(65 + (item.id - 5))}
            </div>
          </motion.div>
        ))}
      </div>

      {/* 反馈信息 */}
      <AnimatePresence>
        {feedback && (
          <motion.div
            className="absolute bottom-16 left-1/2 transform -translate-x-1/2 bg-white rounded-lg shadow-lg p-3 border-2 border-purple-300"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <p className="text-sm font-medium text-purple-800">{feedback}</p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 游戏状态 */}
      <div className="absolute bottom-4 left-4 bg-white rounded-lg p-2 shadow-md">
        <div className="flex space-x-4 text-sm">
          <span className="text-purple-600">
            尝试次数: <span className="font-bold">{attempts}</span>
          </span>
          <span className="text-purple-600">
            得分: <span className="font-bold">{score}</span>
          </span>
        </div>
      </div>

      {/* 控制按钮 */}
      <div className="absolute bottom-4 right-4 flex space-x-2">
        <button
          onClick={resetGame}
          className="w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center hover:bg-orange-600 transition-colors text-sm"
        >
          🔄
        </button>
        {onNext && isComplete && (
          <button
            onClick={onNext}
            className="px-3 py-1 bg-purple-500 text-white text-xs rounded hover:bg-purple-600 transition-colors"
          >
            下一个
          </button>
        )}
      </div>

      {/* 游戏说明 */}
      <div className="absolute top-16 right-4 bg-white rounded-lg p-3 shadow-md max-w-48">
        <h4 className="text-sm font-bold text-purple-800 mb-2">游戏规则</h4>
        <ul className="text-xs text-purple-600 space-y-1">
          <li>• 点击左边的圆形开始连线</li>
          <li>• 将圆形与对应的三角形连接</li>
          <li>• 建立一一对应关系</li>
          <li>• 完成所有连接即可获胜</li>
        </ul>
      </div>
    </div>
  );
};

export default CorrespondenceGame;