import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface ShepherdAnimationProps {
  onComplete: () => void;
  onNext?: () => void;
}

interface Sheep {
  id: number;
  x: number;
  y: number;
  isOut: boolean;
}

interface Stone {
  id: number;
  visible: boolean;
}

const ShepherdAnimation: React.FC<ShepherdAnimationProps> = ({ onComplete, onNext }) => {
  const [scene, setScene] = useState(1); // 1: 介绍, 2: 出门, 3: 回家
  const [sheep, setSheep] = useState<Sheep[]>([]);
  const [stones, setStones] = useState<Stone[]>([]);
  const [narration, setNarration] = useState('');
  const [isPlaying, setIsPlaying] = useState(true);
  const [sheepCount, setSheepCount] = useState(0);

  // 初始化羊群
  useEffect(() => {
    const initialSheep: Sheep[] = Array.from({ length: 8 }, (_, i) => ({
      id: i,
      x: 100 + (i % 4) * 60,
      y: 200 + Math.floor(i / 4) * 40,
      isOut: false
    }));
    setSheep(initialSheep);
    
    const initialStones: Stone[] = Array.from({ length: 8 }, (_, i) => ({
      id: i,
      visible: false
    }));
    setStones(initialStones);
  }, []);

  // 场景控制
  useEffect(() => {
    if (!isPlaying) return;

    const sceneTimers: { [key: number]: NodeJS.Timeout } = {};

    switch (scene) {
      case 1:
        setNarration('很久很久以前，有一个牧羊人，他每天带着羊群出去放牧...');
        sceneTimers[1] = setTimeout(() => setScene(2), 3000);
        break;

      case 2:
        setNarration('出门时，每出去一只羊，他就往口袋里放一颗石头...');
        // 开始羊群出门动画
        startSheepExitAnimation();
        sceneTimers[2] = setTimeout(() => setScene(3), 8000);
        break;

      case 3:
        setNarration('回家时，每进来一只羊，他就扔掉一颗石头...');
        // 开始羊群回家动画
        startSheepReturnAnimation();
        sceneTimers[3] = setTimeout(() => {
          setNarration('通过这种方法，牧羊人不用数字就能知道羊有没有丢失！');
          setTimeout(onComplete, 2000);
        }, 8000);
        break;
    }

    return () => {
      Object.values(sceneTimers).forEach(timer => clearTimeout(timer));
    };
  }, [scene, isPlaying]);

  const startSheepExitAnimation = () => {
    sheep.forEach((_, index) => {
      setTimeout(() => {
        setSheep(prev => prev.map((s, i) => 
          i === index ? { ...s, isOut: true, x: 600, y: 300 } : s
        ));
        
        setStones(prev => prev.map((stone, i) => 
          i === index ? { ...stone, visible: true } : stone
        ));
        
        setSheepCount(index + 1);
      }, index * 800);
    });
  };

  const startSheepReturnAnimation = () => {
    sheep.forEach((_, index) => {
      setTimeout(() => {
        setSheep(prev => prev.map((s, i) => 
          i === index ? { ...s, isOut: false, x: 100 + (i % 4) * 60, y: 200 + Math.floor(i / 4) * 40 } : s
        ));
        
        setStones(prev => prev.map((stone, i) => 
          i === (sheep.length - 1 - index) ? { ...stone, visible: false } : stone
        ));
        
        setSheepCount(sheep.length - 1 - index);
      }, index * 800);
    });
  };

  const togglePlay = () => {
    setIsPlaying(!isPlaying);
  };

  const resetAnimation = () => {
    setScene(1);
    setSheepCount(0);
    setSheep(prev => prev.map(s => ({ ...s, isOut: false, x: 100 + (s.id % 4) * 60, y: 200 + Math.floor(s.id / 4) * 40 })));
    setStones(prev => prev.map(s => ({ ...s, visible: false })));
    setIsPlaying(true);
  };

  return (
    <div className="w-full h-96 bg-gradient-to-b from-blue-200 to-green-200 rounded-lg relative overflow-hidden border-4 border-brown-300">
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        {/* 云朵 */}
        <div className="absolute top-4 left-20 w-16 h-8 bg-white rounded-full opacity-80"></div>
        <div className="absolute top-6 right-32 w-20 h-10 bg-white rounded-full opacity-80"></div>
        
        {/* 山峦 */}
        <div className="absolute bottom-0 left-0 w-full h-20 bg-gradient-to-t from-green-400 to-green-300"></div>
        
        {/* 牧羊人 */}
        <motion.div 
          className="absolute bottom-20 left-10 w-12 h-16 bg-brown-400 rounded-t-full flex flex-col items-center"
          animate={{ rotate: scene === 2 ? [0, 10, 0] : 0 }}
          transition={{ duration: 0.5, repeat: scene === 2 ? Infinity : 0 }}
        >
          {/* 头部 */}
          <div className="w-8 h-8 bg-tan-300 rounded-full mt-1"></div>
          {/* 帽子 */}
          <div className="w-10 h-3 bg-brown-600 rounded-full -mt-2"></div>
          {/* 手杖 */}
          <div className="absolute -right-3 top-6 w-1 h-8 bg-brown-600 rounded"></div>
        </motion.div>
        
        {/* 房子 */}
        <div className="absolute bottom-16 right-10 w-20 h-16">
          <div className="w-full h-12 bg-red-400 rounded-t-lg"></div>
          <div className="w-full h-4 bg-brown-500"></div>
          <div className="absolute top-8 left-2 w-4 h-6 bg-blue-600 rounded"></div>
        </div>
      </div>

      {/* 羊群 */}
      <AnimatePresence>
        {sheep.map((sheepItem) => (
          <motion.div
            key={sheepItem.id}
            className="absolute w-8 h-6 bg-white rounded-full border-2 border-gray-300"
            initial={{ x: sheepItem.x, y: sheepItem.y }}
            animate={{ 
              x: sheepItem.isOut ? sheepItem.x : sheepItem.x,
              y: sheepItem.isOut ? sheepItem.y : sheepItem.y 
            }}
            transition={{ duration: 0.8, ease: "easeInOut" }}
          >
            {/* 羊头 */}
            <div className="absolute -top-2 left-1 w-3 h-3 bg-gray-100 rounded-full"></div>
            {/* 眼睛 */}
            <div className="absolute -top-1 left-2 w-1 h-1 bg-black rounded-full"></div>
            {/* 腿 */}
            <div className="absolute -bottom-1 left-1 w-1 h-2 bg-gray-400"></div>
            <div className="absolute -bottom-1 left-4 w-1 h-2 bg-gray-400"></div>
          </motion.div>
        ))}
      </AnimatePresence>

      {/* 石头口袋 */}
      <div className="absolute top-4 right-4 w-24 h-20 bg-brown-300 rounded-lg border-2 border-brown-500 p-2">
        <h4 className="text-xs font-bold text-center mb-1">石头口袋</h4>
        <div className="grid grid-cols-4 gap-1">
          {stones.map((stone) => (
            <motion.div
              key={stone.id}
              className={`w-3 h-3 rounded-full ${stone.visible ? 'bg-gray-600' : 'bg-transparent'}`}
              initial={{ scale: 0 }}
              animate={{ scale: stone.visible ? 1 : 0 }}
              transition={{ duration: 0.3 }}
            />
          ))}
        </div>
        <div className="text-center text-sm font-bold mt-1">
          {sheepCount} 颗石头
        </div>
      </div>

      {/* 旁白文字 */}
      <div className="absolute bottom-4 left-4 right-4 bg-black bg-opacity-70 text-white p-3 rounded-lg">
        <p className="text-sm leading-relaxed">{narration}</p>
      </div>

      {/* 控制按钮 */}
      <div className="absolute top-4 left-4 flex space-x-2">
        <button
          onClick={togglePlay}
          className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors"
        >
          {isPlaying ? '⏸️' : '▶️'}
        </button>
        <button
          onClick={resetAnimation}
          className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center hover:bg-green-600 transition-colors"
        >
          🔄
        </button>
        {onNext && (
          <button
            onClick={onNext}
            className="px-3 py-1 bg-purple-500 text-white text-xs rounded hover:bg-purple-600 transition-colors"
          >
            下一个
          </button>
        )}
      </div>

      {/* 场景指示器 */}
      <div className="absolute top-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
        {[1, 2, 3].map((sceneNum) => (
          <div
            key={sceneNum}
            className={`w-3 h-3 rounded-full ${
              scene === sceneNum ? 'bg-yellow-400' : 'bg-gray-300'
            }`}
          />
        ))}
      </div>
    </div>
  );
};

export default ShepherdAnimation;