import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface KidsShepherdAnimationProps {
  onComplete: () => void;
  onNext?: () => void;
}

const KidsShepherdAnimation: React.FC<KidsShepherdAnimationProps> = ({ onComplete, onNext }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [sheepCount, setSheepCount] = useState(5);
  const [stonesCount, setStonesCount] = useState(0);
  const [showCelebration, setShowCelebration] = useState(false);
  const [currentMessage, setCurrentMessage] = useState('');

  const messages = [
    "🌟 嗨！小朋友们！我是智慧爷爷！让我们一起听个超级有趣的故事吧！",
    "👴 很久很久以前，有个聪明的牧羊人爷爷，他有很多可爱的小羊羊！",
    "🌅 每天早上，爷爷要带小羊羊们出去玩耍和吃草！",
    "✨ 爷爷有个神奇的方法！每只小羊羊出门，他就放一颗漂亮的石头到魔法袋子里！",
    "🌙 晚上回家时，每只小羊羊回来，爷爷就拿出一颗石头！",
    "🎉 这样爷爷就知道所有小羊羊都安全回家了！真聪明对不对？"
  ];

  useEffect(() => {
    if (currentStep < messages.length) {
      setCurrentMessage(messages[currentStep]);
    }
  }, [currentStep]);

  const handleStart = () => {
    setIsPlaying(true);
    playStory();
  };

  const playStory = async () => {
    // 步骤1：介绍
    setCurrentStep(0);
    await sleep(3000);
    
    // 步骤2：显示牧羊人和羊群
    setCurrentStep(1);
    await sleep(3000);
    
    // 步骤3：准备出门
    setCurrentStep(2);
    await sleep(2000);
    
    // 步骤4：羊群出门，添加石头
    setCurrentStep(3);
    for (let i = 0; i < sheepCount; i++) {
      await sleep(800);
      setStonesCount(i + 1);
      // 播放音效
      playSound('stone');
    }
    
    await sleep(2000);
    
    // 步骤5：羊群回家，移除石头
    setCurrentStep(4);
    for (let i = sheepCount - 1; i >= 0; i--) {
      await sleep(800);
      setStonesCount(i);
      // 播放音效
      playSound('sheep');
    }
    
    // 步骤6：完成庆祝
    setCurrentStep(5);
    setShowCelebration(true);
    playSound('success');
    
    await sleep(3000);
    setShowCelebration(false);
    onComplete();
  };

  const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

  const playSound = (type: 'stone' | 'sheep' | 'success') => {
    // 这里可以添加真实的音效
    console.log(`🔊 Playing ${type} sound!`);
  };

  const handleReset = () => {
    setCurrentStep(0);
    setIsPlaying(false);
    setStonesCount(0);
    setShowCelebration(false);
  };

  return (
    <div className="w-full min-h-[600px] bg-gradient-to-b from-sky-300 via-sky-200 to-green-300 rounded-3xl relative overflow-hidden shadow-2xl border-8 border-yellow-400">
      
      {/* 魔法背景 */}
      <div className="absolute inset-0">
        {/* 动态太阳 */}
        <motion.div 
          className="absolute top-8 right-12 w-24 h-24 bg-gradient-to-br from-yellow-300 to-yellow-500 rounded-full shadow-xl"
          animate={{ 
            rotate: 360,
            scale: [1, 1.1, 1]
          }}
          transition={{ 
            rotate: { duration: 20, repeat: Infinity, ease: "linear" },
            scale: { duration: 3, repeat: Infinity }
          }}
        >
          <div className="absolute inset-3 bg-yellow-200 rounded-full flex items-center justify-center text-3xl">
            😊
          </div>
        </motion.div>
        
        {/* 飘动的云朵 */}
        <motion.div 
          className="absolute top-16 left-24 text-5xl filter drop-shadow-lg"
          animate={{ x: [0, 30, 0], y: [0, -10, 0] }}
          transition={{ duration: 6, repeat: Infinity }}
        >
          ☁️
        </motion.div>
        <motion.div 
          className="absolute top-20 left-80 text-4xl filter drop-shadow-lg"
          animate={{ x: [0, -20, 0], y: [0, 5, 0] }}
          transition={{ duration: 4, repeat: Infinity }}
        >
          ☁️
        </motion.div>
        
        {/* 彩虹 */}
        <div className="absolute top-24 left-1/2 transform -translate-x-1/2 w-64 h-32 opacity-60">
          <div className="w-full h-full rounded-t-full bg-gradient-to-r from-red-400 via-yellow-400 via-green-400 via-blue-400 to-purple-400"></div>
        </div>
        
        {/* 草地装饰 */}
        <div className="absolute bottom-0 w-full h-32 bg-gradient-to-t from-green-400 to-green-300 rounded-t-3xl">
          <div className="absolute inset-0 bg-green-300 opacity-50 rounded-t-3xl"></div>
          {/* 小花朵 */}
          <motion.div 
            className="absolute bottom-8 left-16 text-3xl"
            animate={{ rotate: [0, 10, -10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            🌸
          </motion.div>
          <motion.div 
            className="absolute bottom-12 left-48 text-2xl"
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 1.5, repeat: Infinity }}
          >
            🌼
          </motion.div>
          <motion.div 
            className="absolute bottom-6 right-32 text-3xl"
            animate={{ rotate: [0, -15, 15, 0] }}
            transition={{ duration: 2.5, repeat: Infinity }}
          >
            🌺
          </motion.div>
        </div>
      </div>

      {/* 超可爱的牧羊人爷爷 */}
      <motion.div
        className="absolute bottom-32 left-16 text-8xl filter drop-shadow-2xl cursor-pointer"
        animate={{ 
          scale: [1, 1.05, 1],
          rotate: currentStep >= 3 ? [0, 5, -5, 0] : 0
        }}
        transition={{ 
          scale: { duration: 2, repeat: Infinity },
          rotate: { duration: 1, repeat: Infinity }
        }}
        whileHover={{ scale: 1.1 }}
        onClick={() => playSound('sheep')}
      >
        👴
      </motion.div>

      {/* 可爱的羊群 */}
      <div className="absolute bottom-32 left-32 flex space-x-4">
        {Array.from({ length: sheepCount }, (_, i) => (
          <motion.div
            key={i}
            className="text-6xl filter drop-shadow-lg cursor-pointer"
            initial={{ scale: 0, rotate: 180 }}
            animate={{ 
              scale: currentStep >= 1 ? 1 : 0,
              rotate: 0,
              y: Math.sin(Date.now() / 1000 + i) * 8
            }}
            transition={{ 
              duration: 0.8,
              delay: i * 0.2,
              type: "spring",
              bounce: 0.6
            }}
            whileHover={{ scale: 1.2, rotate: [0, -10, 10, 0] }}
            onClick={() => playSound('sheep')}
          >
            {i % 2 === 0 ? '🐑' : '🐏'}
          </motion.div>
        ))}
      </div>

      {/* 超级魔法石头袋 */}
      <div className="absolute top-8 left-8 bg-gradient-to-br from-purple-500 to-purple-700 rounded-3xl p-8 min-w-48 shadow-2xl border-4 border-purple-300">
        <div className="flex items-center mb-4">
          <motion.span 
            className="text-4xl mr-3"
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            ✨
          </motion.span>
          <h3 className="text-white text-xl font-bold">魔法袋</h3>
        </div>
        
        <div className="grid grid-cols-4 gap-3 mb-4">
          {Array.from({ length: Math.max(stonesCount, 5) }, (_, i) => (
            <motion.div
              key={i}
              className={`w-10 h-10 rounded-full shadow-lg border-3 cursor-pointer ${
                i < stonesCount 
                  ? 'bg-gradient-to-br from-gray-300 to-gray-500 border-gray-200' 
                  : 'bg-transparent border-dashed border-gray-400'
              }`}
              initial={{ scale: 0, rotate: 180 }}
              animate={{ 
                scale: i < stonesCount ? 1 : 0.3,
                rotate: 0
              }}
              transition={{ 
                duration: 0.5,
                type: "spring",
                bounce: 0.8,
                delay: i * 0.1
              }}
              whileHover={i < stonesCount ? { scale: 1.2 } : {}}
              onClick={() => i < stonesCount && playSound('stone')}
            >
              {i < stonesCount && (
                <div className="w-full h-full rounded-full bg-gradient-to-br from-white to-transparent opacity-40"></div>
              )}
            </motion.div>
          ))}
        </div>
        
        <div className="text-center">
          <div className="text-yellow-200 text-2xl font-bold bg-purple-600 rounded-2xl py-2 px-4">
            {stonesCount} 颗石头 💎
          </div>
        </div>
      </div>

      {/* 庆祝烟花效果 */}
      <AnimatePresence>
        {showCelebration && (
          <motion.div
            className="absolute inset-0 flex items-center justify-center pointer-events-none"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="text-9xl"
              animate={{ 
                scale: [0, 1.2, 1],
                rotate: [0, 360]
              }}
              transition={{ duration: 1 }}
            >
              🎉
            </motion.div>
            
            {/* 飞舞的星星 */}
            {Array.from({ length: 8 }, (_, i) => (
              <motion.div
                key={i}
                className="absolute text-4xl"
                initial={{ 
                  x: 0, 
                  y: 0, 
                  scale: 0 
                }}
                animate={{ 
                  x: Math.cos(i * 45 * Math.PI / 180) * 200,
                  y: Math.sin(i * 45 * Math.PI / 180) * 200,
                  scale: [0, 1, 0],
                  rotate: 360
                }}
                transition={{ 
                  duration: 2,
                  delay: i * 0.1
                }}
              >
                ⭐
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* 超级可爱的故事文本框 */}
      <div className="absolute bottom-8 left-8 right-8 bg-gradient-to-r from-pink-400 to-purple-400 rounded-3xl p-6 shadow-2xl border-4 border-white">
        <div className="bg-white rounded-2xl p-6">
          <div className="flex items-center mb-3">
            <motion.div
              className="text-3xl mr-3"
              animate={{ bounce: [0, -10, 0] }}
              transition={{ duration: 1, repeat: Infinity }}
            >
              💬
            </motion.div>
            <h4 className="text-purple-800 text-lg font-bold">智慧爷爷说：</h4>
          </div>
          <p className="text-gray-800 text-xl font-medium leading-relaxed">
            {currentMessage}
          </p>
        </div>
      </div>

      {/* 超大超可爱的控制按钮 */}
      <div className="absolute top-8 right-8 flex flex-col space-y-4">
        <motion.button
          onClick={handleStart}
          disabled={isPlaying}
          className={`w-20 h-20 rounded-full text-4xl font-bold shadow-2xl border-6 ${
            isPlaying 
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed border-gray-400' 
              : 'bg-gradient-to-br from-green-400 to-green-600 text-white hover:from-green-500 hover:to-green-700 border-green-300'
          }`}
          whileHover={!isPlaying ? { scale: 1.1, rotate: [0, -5, 5, 0] } : {}}
          whileTap={!isPlaying ? { scale: 0.95 } : {}}
        >
          {isPlaying ? '⏸️' : '▶️'}
        </motion.button>
        
        <motion.button
          onClick={handleReset}
          className="w-20 h-20 rounded-full bg-gradient-to-br from-orange-400 to-orange-600 text-white text-4xl font-bold shadow-2xl border-6 border-orange-300 hover:from-orange-500 hover:to-orange-700"
          whileHover={{ scale: 1.1, rotate: [0, 10, -10, 0] }}
          whileTap={{ scale: 0.95 }}
        >
          🔄
        </motion.button>
        
        <motion.button
          onClick={() => onNext && onNext()}
          className="w-20 h-20 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 text-white text-3xl font-bold shadow-2xl border-6 border-blue-300 hover:from-blue-500 hover:to-blue-700"
          whileHover={{ scale: 1.1, x: [0, 5, 0] }}
          whileTap={{ scale: 0.95 }}
        >
          ➡️
        </motion.button>
      </div>
    </div>
  );
};

export default KidsShepherdAnimation;
