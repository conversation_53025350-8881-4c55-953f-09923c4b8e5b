import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Lesson01 from '@/components/Lessons/Lesson01/Lesson01';

const Lesson01Demo: React.FC = () => {
  const [isCompleted, setIsCompleted] = useState(false);
  const [scores, setScores] = useState<number[]>([]);

  const handleLessonComplete = (lessonId: string, lessonScores: number[]) => {
    console.log('第1讲完成！', { lessonId, scores: lessonScores });
    setScores(lessonScores);
    setIsCompleted(true);
  };

  const handleRestart = () => {
    setIsCompleted(false);
    setScores([]);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      {/* 头部导航 */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">
                胡小群数学学习平台
              </h1>
              <span className="ml-4 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                演示版本
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                第1讲：对应与计数
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {!isCompleted ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Lesson01 onComplete={handleLessonComplete} />
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="text-center py-12"
          >
            <div className="bg-white rounded-2xl shadow-xl p-8 max-w-2xl mx-auto">
              <div className="mb-6">
                <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <h2 className="text-3xl font-bold text-gray-900 mb-2">
                  恭喜完成第1讲！
                </h2>
                <p className="text-gray-600">
                  你已经掌握了对应与计数的基本概念
                </p>
              </div>

              {/* 成绩展示 */}
              <div className="bg-gray-50 rounded-xl p-6 mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">学习成果</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {scores[0] || 0}分
                    </div>
                    <div className="text-sm text-gray-600">牧羊人故事</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {scores[1] || 0}分
                    </div>
                    <div className="text-sm text-gray-600">连线配对</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {scores[2] || 0}分
                    </div>
                    <div className="text-sm text-gray-600">椅子游戏</div>
                  </div>
                </div>
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-indigo-600">
                      {Math.round((scores.reduce((a, b) => a + b, 0) / scores.length) || 0)}分
                    </div>
                    <div className="text-sm text-gray-600">总体评分</div>
                  </div>
                </div>
              </div>

              {/* 学习要点总结 */}
              <div className="text-left mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">本讲要点回顾</h3>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    理解了对应关系的基本概念
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    学会了通过一一对应来比较数量
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-purple-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    体验了数学思维在生活中的应用
                  </li>
                </ul>
              </div>

              {/* 操作按钮 */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={handleRestart}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  重新学习
                </button>
                <button
                  onClick={() => window.location.reload()}
                  className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium"
                >
                  返回首页
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </div>

      {/* 底部信息 */}
      <div className="bg-white border-t mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center text-gray-500 text-sm">
            <p>基于胡小群老师教育理念的数学学习平台</p>
            <p className="mt-1">强调数学思维培养，注重理解过程</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Lesson01Demo;
