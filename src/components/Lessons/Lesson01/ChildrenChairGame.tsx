import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface ChildrenChairGameProps {
  onComplete: (score: number) => void;
  onNext?: () => void;
}

interface Child {
  id: number;
  name: string;
  x: number;
  y: number;
  isSeated: boolean;
  targetChairId?: number;
}

interface Chair {
  id: number;
  x: number;
  y: number;
  isOccupied: boolean;
  occupiedBy?: number;
}

const ChildrenChairGame: React.FC<ChildrenChairGameProps> = ({ onComplete, onNext }) => {
  const [children, setChildren] = useState<Child[]>([]);
  const [chairs, setChairs] = useState<Chair[]>([]);
  const [draggedChild, setDraggedChild] = useState<number | null>(null);
  const [gamePhase, setGamePhase] = useState<'setup' | 'seating' | 'complete'>('setup');
  const [score, setScore] = useState(0);
  const [feedback, setFeedback] = useState('');
  const [childrenCount, setChildrenCount] = useState(4);

  const childNames = ['小明', '小红', '小华', '小丽', '小刚', '小美'];
  const childColors = ['bg-blue-400', 'bg-pink-400', 'bg-green-400', 'bg-yellow-400', 'bg-purple-400', 'bg-red-400'];

  // 初始化游戏
  useEffect(() => {
    initializeGame();
  }, [childrenCount]);

  const initializeGame = () => {
    // 创建孩子
    const newChildren: Child[] = Array.from({ length: childrenCount }, (_, i) => ({
      id: i,
      name: childNames[i],
      x: 50 + (i % 3) * 80,
      y: 50 + Math.floor(i / 3) * 100,
      isSeated: false
    }));

    // 创建椅子
    const newChairs: Chair[] = Array.from({ length: childrenCount }, (_, i) => ({
      id: i,
      x: 300 + (i % 3) * 80,
      y: 100 + Math.floor(i / 3) * 100,
      isOccupied: false
    }));

    setChildren(newChildren);
    setChairs(newChairs);
    setGamePhase('seating');
    setScore(0);
    setFeedback('帮助每个孩子找到一把椅子坐下！');
  };

  // 检查游戏完成状态
  useEffect(() => {
    if (gamePhase === 'seating' && children.length > 0) {
      const allSeated = children.every(child => child.isSeated);
      
      if (allSeated) {
        setGamePhase('complete');
        const finalScore = 100;
        setScore(finalScore);
        setFeedback('太棒了！每个孩子都有椅子坐，这就是一一对应！');
        setTimeout(() => onComplete(finalScore), 2000);
      }
    }
  }, [children, gamePhase, onComplete]);

  const handleDragStart = (childId: number) => {
    const child = children.find(c => c.id === childId);
    if (child && !child.isSeated) {
      setDraggedChild(childId);
    }
  };

  const handleDragEnd = () => {
    setDraggedChild(null);
  };

  const handleChairClick = (chairId: number) => {
    if (draggedChild === null) return;
    
    const chair = chairs.find(c => c.id === chairId);
    const child = children.find(c => c.id === draggedChild);
    
    if (!chair || !child || chair.isOccupied || child.isSeated) return;

    // 将孩子放到椅子上
    setChildren(prev => prev.map(c => 
      c.id === draggedChild 
        ? { ...c, isSeated: true, targetChairId: chairId, x: chair.x, y: chair.y - 30 }
        : c
    ));

    setChairs(prev => prev.map(c => 
      c.id === chairId 
        ? { ...c, isOccupied: true, occupiedBy: draggedChild }
        : c
    ));

    setDraggedChild(null);
  };

  const handleChildClick = (childId: number) => {
    const child = children.find(c => c.id === childId);
    if (!child || !child.isSeated) return;

    // 将孩子从椅子上移开
    const originalX = 50 + (childId % 3) * 80;
    const originalY = 50 + Math.floor(childId / 3) * 100;

    setChildren(prev => prev.map(c => 
      c.id === childId 
        ? { ...c, isSeated: false, targetChairId: undefined, x: originalX, y: originalY }
        : c
    ));

    setChairs(prev => prev.map(c => 
      c.occupiedBy === childId 
        ? { ...c, isOccupied: false, occupiedBy: undefined }
        : c
    ));
  };

  const addChild = () => {
    if (childrenCount < 6) {
      setChildrenCount(prev => prev + 1);
    }
  };

  const removeChild = () => {
    if (childrenCount > 2) {
      setChildrenCount(prev => prev - 1);
    }
  };

  const resetGame = () => {
    initializeGame();
  };

  return (
    <div className="w-full h-96 bg-gradient-to-br from-yellow-100 to-orange-100 rounded-lg relative overflow-hidden border-4 border-orange-300">
      {/* 游戏标题 */}
      <div className="absolute top-4 left-1/2 transform -translate-x-1/2 text-center">
        <h3 className="text-lg font-bold text-orange-800 mb-1">孩子与椅子</h3>
        <p className="text-sm text-orange-600">拖拽孩子到椅子上，体验一一对应关系</p>
      </div>

      {/* 左侧 - 孩子区域 */}
      <div className="absolute left-4 top-16 w-48 h-64 bg-white bg-opacity-50 rounded-lg border-2 border-dashed border-orange-300 p-2">
        <h4 className="text-sm font-bold text-orange-700 mb-2 text-center">孩子们</h4>
        {children.map((child, index) => (
          <motion.div
            key={child.id}
            className={`absolute w-12 h-16 ${childColors[index]} rounded-lg cursor-pointer border-2 border-white shadow-md flex flex-col items-center justify-center ${
              child.isSeated ? 'opacity-50' : 'hover:scale-110'
            }`}
            style={{ 
              left: child.isSeated ? child.x - 200 : child.x - 20, 
              top: child.isSeated ? child.y - 16 : child.y - 16 
            }}
            onMouseDown={() => handleDragStart(child.id)}
            onMouseUp={handleDragEnd}
            onClick={() => handleChildClick(child.id)}
            whileHover={{ scale: child.isSeated ? 1 : 1.1 }}
            whileTap={{ scale: 0.95 }}
            animate={{
              x: child.isSeated ? child.x - 200 : child.x - 20,
              y: child.isSeated ? child.y - 16 : child.y - 16
            }}
            transition={{ duration: 0.5, ease: "easeInOut" }}
          >
            {/* 头部 */}
            <div className="w-6 h-6 bg-yellow-200 rounded-full border border-gray-300 mb-1">
              <div className="w-full h-full flex items-center justify-center text-xs">😊</div>
            </div>
            {/* 身体 */}
            <div className="w-8 h-6 rounded"></div>
            {/* 名字 */}
            <div className="absolute -bottom-4 text-xs font-bold text-gray-700 whitespace-nowrap">
              {child.name}
            </div>
          </motion.div>
        ))}
      </div>

      {/* 右侧 - 椅子区域 */}
      <div className="absolute right-4 top-16 w-48 h-64 bg-white bg-opacity-50 rounded-lg border-2 border-dashed border-orange-300 p-2">
        <h4 className="text-sm font-bold text-orange-700 mb-2 text-center">椅子们</h4>
        {chairs.map((chair) => (
          <motion.div
            key={chair.id}
            className={`absolute w-12 h-12 cursor-pointer transition-all ${
              chair.isOccupied ? 'opacity-70' : 'hover:scale-105'
            }`}
            style={{ left: chair.x - 300, top: chair.y - 16 }}
            onClick={() => handleChairClick(chair.id)}
            whileHover={{ scale: chair.isOccupied ? 1 : 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {/* 椅背 */}
            <div className="absolute w-10 h-8 bg-brown-400 rounded-t-lg border-2 border-brown-600"></div>
            {/* 座椅 */}
            <div className="absolute top-6 w-12 h-6 bg-brown-400 rounded border-2 border-brown-600"></div>
            {/* 椅腿 */}
            <div className="absolute top-10 left-1 w-1 h-4 bg-brown-600"></div>
            <div className="absolute top-10 right-1 w-1 h-4 bg-brown-600"></div>
            
            {/* 占用指示器 */}
            {chair.isOccupied && (
              <div className="absolute -top-2 -right-2 w-4 h-4 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                <span className="text-xs text-white">✓</span>
              </div>
            )}
          </motion.div>
        ))}
      </div>

      {/* 反馈信息 */}
      <AnimatePresence>
        {feedback && (
          <motion.div
            className="absolute bottom-20 left-1/2 transform -translate-x-1/2 bg-white rounded-lg shadow-lg p-3 border-2 border-orange-300 max-w-80"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <p className="text-sm font-medium text-orange-800 text-center">{feedback}</p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 控制面板 */}
      <div className="absolute top-4 left-4 bg-white rounded-lg p-2 shadow-md">
        <div className="flex items-center space-x-2 mb-2">
          <span className="text-sm font-medium text-orange-700">孩子数量:</span>
          <button
            onClick={removeChild}
            disabled={childrenCount <= 2}
            className="w-6 h-6 bg-red-500 text-white rounded text-sm hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            -
          </button>
          <span className="w-8 text-center font-bold text-orange-800">{childrenCount}</span>
          <button
            onClick={addChild}
            disabled={childrenCount >= 6}
            className="w-6 h-6 bg-green-500 text-white rounded text-sm hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            +
          </button>
        </div>
        <div className="text-xs text-orange-600 text-center">
          椅子数量: {childrenCount}
        </div>
      </div>

      {/* 游戏状态 */}
      <div className="absolute bottom-4 left-4 bg-white rounded-lg p-2 shadow-md">
        <div className="flex space-x-4 text-sm">
          <span className="text-orange-600">
            已就座: <span className="font-bold">{children.filter(c => c.isSeated).length}/{children.length}</span>
          </span>
          <span className="text-orange-600">
            得分: <span className="font-bold">{score}</span>
          </span>
        </div>
      </div>

      {/* 控制按钮 */}
      <div className="absolute bottom-4 right-4 flex space-x-2">
        <button
          onClick={resetGame}
          className="w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center hover:bg-orange-600 transition-colors text-sm"
        >
          🔄
        </button>
        {onNext && gamePhase === 'complete' && (
          <button
            onClick={onNext}
            className="px-3 py-1 bg-orange-500 text-white text-xs rounded hover:bg-orange-600 transition-colors"
          >
            下一个
          </button>
        )}
      </div>

      {/* 游戏说明 */}
      <div className="absolute top-20 right-4 bg-white rounded-lg p-3 shadow-md max-w-48">
        <h4 className="text-sm font-bold text-orange-800 mb-2">游戏说明</h4>
        <ul className="text-xs text-orange-600 space-y-1">
          <li>• 点击孩子拖拽到椅子上</li>
          <li>• 每个孩子对应一把椅子</li>
          <li>• 点击已就座的孩子可以移开</li>
          <li>• 调整孩子数量观察变化</li>
          <li>• 体验一一对应的概念</li>
        </ul>
      </div>

      {/* 数学概念提示 */}
      {gamePhase === 'complete' && (
        <motion.div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-green-100 border-2 border-green-400 rounded-lg p-4 shadow-lg max-w-80"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 1 }}
        >
          <h4 className="text-lg font-bold text-green-800 mb-2 text-center">数学发现！</h4>
          <p className="text-sm text-green-700 text-center leading-relaxed">
            当孩子的数量等于椅子的数量时，每个孩子都能找到一把椅子，这就叫做<span className="font-bold">一一对应</span>！
          </p>
        </motion.div>
      )}
    </div>
  );
};

export default ChildrenChairGame;