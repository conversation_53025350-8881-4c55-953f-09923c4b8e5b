import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// 通知类型
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number; // 毫秒，0表示不自动消失
  createdAt: string;
}

// 模态框类型
export interface Modal {
  id: string;
  type: string;
  title: string;
  content?: any;
  props?: Record<string, any>;
  closable?: boolean;
}

// 侧边栏状态
export interface Sidebar {
  isOpen: boolean;
  activeSection: string | null;
}

// 主题类型
export type Theme = 'light' | 'dark' | 'auto';

// 语言类型
export type Language = 'zh-CN' | 'en-US';

// UI状态类型
interface UIState {
  theme: Theme;
  language: Language;
  sidebar: Sidebar;
  notifications: Notification[];
  modals: Modal[];
  loading: {
    global: boolean;
    components: Record<string, boolean>;
  };
  errors: {
    global: string | null;
    components: Record<string, string | null>;
  };
  preferences: {
    soundEnabled: boolean;
    animationsEnabled: boolean;
    autoSave: boolean;
    fontSize: 'small' | 'medium' | 'large';
  };
}

// 初始状态
const initialState: UIState = {
  theme: 'light',
  language: 'zh-CN',
  sidebar: {
    isOpen: false,
    activeSection: null,
  },
  notifications: [],
  modals: [],
  loading: {
    global: false,
    components: {},
  },
  errors: {
    global: null,
    components: {},
  },
  preferences: {
    soundEnabled: true,
    animationsEnabled: true,
    autoSave: true,
    fontSize: 'medium',
  },
};

// 创建slice
const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // 主题相关
    setTheme: (state, action: PayloadAction<Theme>) => {
      state.theme = action.payload;
    },
    
    // 语言相关
    setLanguage: (state, action: PayloadAction<Language>) => {
      state.language = action.payload;
    },
    
    // 侧边栏相关
    toggleSidebar: (state) => {
      state.sidebar.isOpen = !state.sidebar.isOpen;
    },
    setSidebarOpen: (state, action: PayloadAction<boolean>) => {
      state.sidebar.isOpen = action.payload;
    },
    setSidebarActiveSection: (state, action: PayloadAction<string | null>) => {
      state.sidebar.activeSection = action.payload;
    },
    
    // 通知相关
    addNotification: (state, action: PayloadAction<Omit<Notification, 'id' | 'createdAt'>>) => {
      const notification: Notification = {
        ...action.payload,
        id: `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        createdAt: new Date().toISOString(),
      };
      state.notifications.push(notification);
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(n => n.id !== action.payload);
    },
    clearNotifications: (state) => {
      state.notifications = [];
    },
    
    // 模态框相关
    openModal: (state, action: PayloadAction<Omit<Modal, 'id'>>) => {
      const modal: Modal = {
        ...action.payload,
        id: `modal-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      };
      state.modals.push(modal);
    },
    closeModal: (state, action: PayloadAction<string>) => {
      state.modals = state.modals.filter(m => m.id !== action.payload);
    },
    closeAllModals: (state) => {
      state.modals = [];
    },
    
    // 加载状态相关
    setGlobalLoading: (state, action: PayloadAction<boolean>) => {
      state.loading.global = action.payload;
    },
    setComponentLoading: (state, action: PayloadAction<{ component: string; loading: boolean }>) => {
      state.loading.components[action.payload.component] = action.payload.loading;
    },
    clearComponentLoading: (state, action: PayloadAction<string>) => {
      delete state.loading.components[action.payload];
    },
    
    // 错误状态相关
    setGlobalError: (state, action: PayloadAction<string | null>) => {
      state.errors.global = action.payload;
    },
    setComponentError: (state, action: PayloadAction<{ component: string; error: string | null }>) => {
      state.errors.components[action.payload.component] = action.payload.error;
    },
    clearComponentError: (state, action: PayloadAction<string>) => {
      delete state.errors.components[action.payload];
    },
    clearAllErrors: (state) => {
      state.errors.global = null;
      state.errors.components = {};
    },
    
    // 偏好设置相关
    updatePreferences: (state, action: PayloadAction<Partial<UIState['preferences']>>) => {
      state.preferences = { ...state.preferences, ...action.payload };
    },
  },
});

// 导出actions
export const {
  setTheme,
  setLanguage,
  toggleSidebar,
  setSidebarOpen,
  setSidebarActiveSection,
  addNotification,
  removeNotification,
  clearNotifications,
  openModal,
  closeModal,
  closeAllModals,
  setGlobalLoading,
  setComponentLoading,
  clearComponentLoading,
  setGlobalError,
  setComponentError,
  clearComponentError,
  clearAllErrors,
  updatePreferences,
} = uiSlice.actions;

// 选择器
export const selectTheme = (state: { ui: UIState }) => state.ui.theme;
export const selectLanguage = (state: { ui: UIState }) => state.ui.language;
export const selectSidebar = (state: { ui: UIState }) => state.ui.sidebar;
export const selectNotifications = (state: { ui: UIState }) => state.ui.notifications;
export const selectModals = (state: { ui: UIState }) => state.ui.modals;
export const selectGlobalLoading = (state: { ui: UIState }) => state.ui.loading.global;
export const selectComponentLoading = (component: string) => (state: { ui: UIState }) => 
  state.ui.loading.components[component] || false;
export const selectGlobalError = (state: { ui: UIState }) => state.ui.errors.global;
export const selectComponentError = (component: string) => (state: { ui: UIState }) => 
  state.ui.errors.components[component] || null;
export const selectPreferences = (state: { ui: UIState }) => state.ui.preferences;

// 导出reducer
export default uiSlice.reducer;
