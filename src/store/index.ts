import { configureStore } from '@reduxjs/toolkit';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';
import authSlice from './slices/authSlice';
import studentSlice from './slices/studentSlice';
import lessonSlice from './slices/lessonSlice';
import uiSlice from './slices/uiSlice';
import progressSlice from './slices/progressSlice';
import achievementSlice from './slices/achievementSlice';

// 配置Redux store
export const store = configureStore({
  reducer: {
    auth: authSlice,
    student: studentSlice,
    lesson: lessonSlice,
    ui: uiSlice,
    progress: progressSlice,
    achievement: achievementSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // 忽略这些action类型的序列化检查
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
        // 忽略这些路径的序列化检查
        ignoredPaths: ['auth.lastActiveAt', 'student.progress.lastStudyDate']
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

// 类型定义
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// 类型化的hooks
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// 状态持久化配置
const persistConfig = {
  key: 'hxq-math',
  storage: typeof window !== 'undefined' ? window.localStorage : undefined,
  whitelist: ['auth', 'student', 'progress'], // 只持久化这些状态
  blacklist: ['ui'], // 不持久化UI状态
};

// 导出store实例
export default store;