import React from 'react';

interface LoadingScreenProps {
  message?: string;
  progress?: number;
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({ 
  message = '正在加载...', 
  progress 
}) => {
  return (
    <div className="fixed inset-0 bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500 flex items-center justify-center z-50">
      <div className="text-center text-white">
        {/* 主要加载动画 */}
        <div className="relative mb-8">
          <div className="w-20 h-20 border-4 border-white border-opacity-30 rounded-full animate-spin border-t-white mx-auto"></div>
          
          {/* 内部旋转圆 */}
          <div className="absolute inset-2 w-16 h-16 border-2 border-white border-opacity-50 rounded-full animate-spin animate-reverse border-b-white"></div>
          
          {/* 中心点 */}
          <div className="absolute inset-6 w-8 h-8 bg-white rounded-full opacity-80 animate-pulse"></div>
        </div>
        
        {/* 加载文本 */}
        <h2 className="text-2xl font-bold mb-4 animate-pulse">
          {message}
        </h2>
        
        {/* 进度条（如果提供了进度） */}
        {typeof progress === 'number' && (
          <div className="w-64 mx-auto">
            <div className="bg-white bg-opacity-30 rounded-full h-2 mb-2">
              <div 
                className="bg-white h-2 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
              ></div>
            </div>
            <p className="text-sm opacity-80">{Math.round(progress)}%</p>
          </div>
        )}
        
        {/* 装饰性数学符号 */}
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          <div className="absolute top-1/4 left-1/4 text-6xl opacity-10 animate-bounce">+</div>
          <div className="absolute top-1/3 right-1/4 text-4xl opacity-10 animate-bounce animation-delay-300">-</div>
          <div className="absolute bottom-1/3 left-1/3 text-5xl opacity-10 animate-bounce animation-delay-700">×</div>
          <div className="absolute bottom-1/4 right-1/3 text-4xl opacity-10 animate-bounce animation-delay-500">÷</div>
          <div className="absolute top-1/2 left-1/6 text-3xl opacity-10 animate-bounce animation-delay-900">=</div>
          <div className="absolute top-1/5 right-1/6 text-3xl opacity-10 animate-bounce animation-delay-1100">123</div>
        </div>
      </div>
    </div>
  );
};

export default LoadingScreen;