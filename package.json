{"name": "hxq-math-platform", "version": "1.0.0", "description": "胡小群数学多媒体学习平台", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "@reduxjs/toolkit": "^1.9.3", "react-redux": "^8.0.5", "three": "^0.150.0", "@react-three/fiber": "^8.11.0", "@react-three/drei": "^9.56.0", "framer-motion": "^10.0.0", "howler": "^2.2.3", "lottie-react": "^2.4.0"}, "devDependencies": {"@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@types/three": "^0.149.0", "@types/howler": "^2.2.7", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "@vitejs/plugin-react": "^3.1.0", "autoprefixer": "^10.4.13", "eslint": "^8.35.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "postcss": "^8.4.21", "tailwindcss": "^3.2.7", "typescript": "^4.9.4", "vite": "^4.1.0", "vitest": "^0.28.5", "@vitest/ui": "^0.28.5"}, "keywords": ["数学教育", "小学数学", "互动学习", "多媒体教学", "React", "Three.js"], "author": "数学教育团队", "license": "MIT"}